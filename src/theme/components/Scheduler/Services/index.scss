@import '../../../globals/Colors.scss';

.setup-services-view {
    .toolbar {
        padding: 0px;
    }

    .table-view {
        .table-data-list-view {
            .table-data-cell {
                .name-frame {
                    display: flex;
                    align-items: center;

                    .avatar {
                        flex: 0 0 32px;
                    }
                }

                .full-name {
                    margin-left: 12px;
                }
            }
        }
    }

    .hint-frame {
        display: flex;
        font-size: 15px;
        font-weight: 400;
        width: 100%;

        .hint-button {
            font-size: 15px;
            color: $orange;
            border: none;
            margin-left: 5px;
            background: none;
        }
    }
}