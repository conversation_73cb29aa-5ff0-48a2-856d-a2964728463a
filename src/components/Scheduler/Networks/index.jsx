import { useEffect, useState, useMemo, useCallback } from "react"
import { useNavigate } from "react-router"
import { debounce } from "lodash"

import OrganizationsQuery from "../../../graphql/queries/components/Scheduler/Organizations"
import createKioskClient from "../../../graphql/KioskApolloClient"
import useKioskAuth from "../../../hooks/useKioskAuth"

import LoadingPane from "../../Shared/LoadingPane"
import TableView from "../../Shared/TableView"
import FloatingTextField from "../../Shared/FloatingTextField"
import IconButton from "../../Shared/IconButton"

const SchedulerNetworks = () => {
    const [onload, setOnload] = useState(true)
    const [organizations, setOrganizations] = useState([])
    const [queryLoading, setQueryLoading] = useState(false)

    // Search state
    const [searchTerm, setSearchTerm] = useState('')
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('')

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1)
    const [itemsPerPage] = useState(20)

    const navigate = useNavigate()
    const { loading: authLoading, error: authError, authenticateKiosk } = useKioskAuth()

    // Search utility functions
    const normalizeString = (str) => {
        if (!str) return ''
        return str
            .toLowerCase()
            .trim()
            .replace(/[^\w\s]/g, '') // Remove special characters except spaces
            .replace(/\s+/g, ' ') // Normalize multiple spaces to single space
    }

    const extractZipCodes = (addresses) => {
        if (!addresses || !Array.isArray(addresses)) return []
        return addresses
            .map(addr => addr.zip || addr.address?.match(/\b\d{5}(-\d{4})?\b/)?.[0])
            .filter(Boolean)
    }

    const searchOrganizations = useCallback((orgs, searchTerm) => {
        if (!searchTerm.trim()) return orgs

        const normalizedSearch = normalizeString(searchTerm)

        // Check if search term looks like a ZIP code (5 digits, optionally followed by -4 digits)
        const isZipSearch = /^\d{5}(-\d{4})?$/.test(searchTerm.trim())

        return orgs.filter(org => {
            // ZIP code search - exact matching
            if (isZipSearch) {
                const zipCodes = extractZipCodes(org.addresses)
                return zipCodes.some(zip => zip === searchTerm.trim())
            }

            // Name search - case-insensitive, partial matching
            const normalizedName = normalizeString(org.name)
            const normalizedDesc = normalizeString(org.desc)

            return normalizedName.includes(normalizedSearch) ||
                   normalizedDesc.includes(normalizedSearch)
        }).sort((a, b) => {
            // Sort by relevance: exact match > starts with > contains
            const normalizedSearch = normalizeString(searchTerm)
            const aName = normalizeString(a.name)
            const bName = normalizeString(b.name)

            // Exact match
            if (aName === normalizedSearch) return -1
            if (bName === normalizedSearch) return 1

            // Starts with
            if (aName.startsWith(normalizedSearch) && !bName.startsWith(normalizedSearch)) return -1
            if (bName.startsWith(normalizedSearch) && !aName.startsWith(normalizedSearch)) return 1

            // Alphabetical for same relevance level
            return aName.localeCompare(bName)
        })
    }, [])

    // Debounced search function
    const debouncedSearch = useCallback(
        debounce((term) => {
            setDebouncedSearchTerm(term)
            setCurrentPage(1) // Reset to first page when searching
        }, 250),
        []
    )

    // Handle search input change
    const handleSearchChange = (value) => {
        setSearchTerm(value)
        debouncedSearch(value)
    }

    // Handle organization row click - navigate to services page
    const handleOrganizationClick = (organization) => {
        navigate(`/dashboard/networks/${organization.id}/services`)
    }

    // Authenticate with kiosk user and fetch organizations
    useEffect(() => {
        if (onload) {
            const fetchOrganizations = async () => {
                try {
                    setQueryLoading(true)

                    // Authenticate with kiosk user (no token sent for login)
                    const token = await authenticateKiosk()

                    // Create kiosk client with the token for queries
                    const kioskClient = createKioskClient(token)

                    // Fetch organizations using kiosk authentication
                    const response = await kioskClient.query({
                        query: OrganizationsQuery
                    })

                    if (response.data?.organizations?.nodes) {
                        setOrganizations(response.data.organizations.nodes)
                    }
                } catch (error) {
                    console.error('Failed to fetch organizations:', error)
                    // You might want to show an error message to the user here
                } finally {
                    setQueryLoading(false)
                    setOnload(false)
                }
            }

            fetchOrganizations()
        }
    }, [onload, authenticateKiosk])

    // Filter and paginate organizations
    const filteredOrganizations = useMemo(() => {
        return searchOrganizations(organizations, debouncedSearchTerm)
    }, [organizations, debouncedSearchTerm, searchOrganizations])

    const paginatedOrganizations = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage
        const endIndex = startIndex + itemsPerPage
        return filteredOrganizations.slice(startIndex, endIndex)
    }, [filteredOrganizations, currentPage, itemsPerPage])

    const totalPages = Math.ceil(filteredOrganizations.length / itemsPerPage)

    // Highlight search term in text
    const highlightSearchTerm = (text, searchTerm) => {
        if (!searchTerm.trim() || !text) return text

        // Don't highlight ZIP code searches
        const isZipSearch = /^\d{5}(-\d{4})?$/.test(searchTerm.trim())
        if (isZipSearch) return text

        const normalizedSearch = normalizeString(searchTerm)
        const normalizedText = normalizeString(text)

        if (!normalizedText.includes(normalizedSearch)) return text

        // Find the actual position in the original text
        const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
        const parts = text.split(regex)

        return parts.map((part, index) =>
            regex.test(part) ?
                <mark key={index} style={{ backgroundColor: '#ffeb3b', padding: '0 2px', borderRadius: '2px' }}>{part}</mark> :
                part
        )
    }

    // Format organizations data for table display
    const formattedOrganizations = paginatedOrganizations.map(org => {
        return {
            id: org.id,
            name: org.name,
            nameFrame: (
                <div className='name-frame'>
                    <div className='org-name'>{highlightSearchTerm(org.name, searchTerm)}</div>
                </div>
            ),
            addressFrame: (
                <div className='address-frame'>
                    {org.addresses && org.addresses.length > 0 ? (
                        org.addresses.map((addr, index) => (
                            <div key={index} className='address-item'>{addr.address}</div>
                        ))
                    ) : (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No address</span>
                    )}
                </div>
            ),
            phoneFrame: (
                <div className='phone-frame'>
                    {org.phones && org.phones.length > 0 ? (
                        org.phones.map((phone, index) => (
                            <div key={index} className='phone-item'>{phone.number}</div>
                        ))
                    ) : (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No phone</span>
                    )}
                </div>
            ),
            descFrame: (
                <div className='desc-frame'>
                    {org.desc ? (
                        <div className='org-desc'>{highlightSearchTerm(org.desc, searchTerm)}</div>
                    ) : (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No description</span>
                    )}
                </div>
            )
        }
    })

    // Pagination controls
    const handlePageChange = (page) => {
        setCurrentPage(page)
    }

    const generatePageNumbers = () => {
        const pages = []
        const maxVisiblePages = 5
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2))
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1)

        // Adjust start page if we're near the end
        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1)
        }

        for (let i = startPage; i <= endPage; i++) {
            pages.push(i)
        }

        return pages
    }

    if (onload || authLoading || queryLoading) return <LoadingPane />

    // Show error if kiosk authentication failed
    if (authError) {
        return (
            <div className='networks-view full-screen-view'>
                <div className='toolbar'>
                    <div className='toolbar-header'>Networks - Authentication Error</div>
                </div>
                <div className='content-frame'>
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '200px',
                        color: '#666',
                        fontStyle: 'italic'
                    }}>
                        Failed to authenticate with kiosk user: {authError}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className='networks-view full-screen-view'>
            <div className='toolbar'>
                <div className='toolbar-header'>
                    {`Networks (${filteredOrganizations.length}${filteredOrganizations.length !== organizations.length ? ` of ${organizations.length}` : ''})`}
                </div>
            </div>

            <div className='content-frame'>
                {/* Search Bar */}
                <div className='search-section'>
                    <div className='search-bar'>
                        <FloatingTextField
                            label='Search organizations...'
                            onChange={handleSearchChange}
                            value={searchTerm}
                            placeholder='Search by name or ZIP code (e.g., "Wellup Clinic" or "90210")'
                        />
                        {searchTerm && (
                            <IconButton
                                icon={require('../../../theme/assets/x-icon.png')}
                                onClick={() => {
                                    setSearchTerm('')
                                    setDebouncedSearchTerm('')
                                    setCurrentPage(1)
                                }}
                                title={{
                                    color: '#666',
                                    label: 'Clear'
                                }}
                                className='clear-search-button'
                            />
                        )}
                    </div>

                    {debouncedSearchTerm && (
                        <div className='search-results-info'>
                            {filteredOrganizations.length === 0 ? (
                                <span className='no-results'>No organizations found for "{debouncedSearchTerm}"</span>
                            ) : (
                                <span className='results-count'>
                                    {filteredOrganizations.length} organization{filteredOrganizations.length !== 1 ? 's' : ''} found
                                </span>
                            )}
                        </div>
                    )}
                </div>

                {/* Table */}
                <TableView
                    data={formattedOrganizations}
                    headers={[
                        { title: 'Name', width: '25%' },
                        { title: 'Address', width: '30%' },
                        { title: 'Phone', width: '20%' },
                        { title: 'Description', width: '25%' }
                    ]}
                    keys={['nameFrame', 'addressFrame', 'phoneFrame', 'descFrame']}
                    onCellClick={handleOrganizationClick}
                />

                {/* Pagination */}
                {totalPages > 1 && (
                    <div className='pagination-section'>
                        <div className='pagination-info'>
                            Showing {((currentPage - 1) * itemsPerPage) + 1}-{Math.min(currentPage * itemsPerPage, filteredOrganizations.length)} of {filteredOrganizations.length}
                        </div>

                        <div className='pagination-controls'>
                            <IconButton
                                icon={require('../../../theme/assets/arrow-icon-white.png')}
                                onClick={() => handlePageChange(currentPage - 1)}
                                disabled={currentPage === 1}
                                className={`pagination-button ${currentPage === 1 ? 'disabled' : ''}`}
                                style={{ transform: 'rotate(180deg)' }}
                                title={{ label: 'Previous' }}
                            />

                            {generatePageNumbers().map(page => (
                                <button
                                    key={page}
                                    className={`pagination-number ${page === currentPage ? 'active' : ''}`}
                                    onClick={() => handlePageChange(page)}
                                >
                                    {page}
                                </button>
                            ))}

                            <IconButton
                                icon={require('../../../theme/assets/arrow-icon-white.png')}
                                onClick={() => handlePageChange(currentPage + 1)}
                                disabled={currentPage === totalPages}
                                className={`pagination-button ${currentPage === totalPages ? 'disabled' : ''}`}
                                title={{ label: 'Next' }}
                            />
                        </div>
                    </div>
                )}
            </div>
        </div>
    )
}

export default SchedulerNetworks
