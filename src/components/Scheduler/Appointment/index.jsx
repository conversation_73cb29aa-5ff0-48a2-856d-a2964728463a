import { useEffect, useState } from "react"
import { useNavigate, useParams } from "react-router"
import { useQuery } from "@apollo/client"

import _ from 'lodash'
import moment from 'moment'

import useApptStatus from "../../../hooks/useApptStatus"
import useCurrentRole from "../../../hooks/useCurrentRole"
import useConstants from "../../../hooks/useConstants"

import AppointmentQuery from '../../../graphql/queries/components/Scheduler/Appointment'

import ApptCreate from './Create'

import Breadcrumb from "../../Shared/Breadcrumb"
import HeaderValueLabel from "../../Shared/HeaderValueLabel"
import IconButton from "../../Shared/IconButton"
import LoadingPane from "../../Shared/LoadingPane"
import Avatar from "../../Shared/Avatar"

import AppointmentStatusPane from "./panes/Status"

import AppointmentStatus from "../Appointment/Status"

const Appointment = () => {
    const params = useParams()

    return (params.id === 'create') ?
        <ApptCreate />
        :
        <ApptDetail />
}

const ApptDetail = () => {
    const navigate = useNavigate()
    const params = useParams()

    const { apptCancel, apptCheckIn, apptLeft, apptNoShow, apptStart } = useApptStatus()
    const { currentRole } = useCurrentRole()
    const { apptStatuses } = useConstants()

    const [onload, setOnload] = useState(true)
    const [modal, setModal] = useState(false)
    const [appt, setAppt] = useState()

    const { data, loading } = useQuery(AppointmentQuery, {
        variables: { id: currentRole?.org?.id, appt: params.appt },
        skip: !currentRole?.org?.id
    })

    useEffect(() => {
        if (onload && !loading) {
            if (!data) {
                navigate('/dashboard/appointments')
                return
            }

            setAppt(data.org.appointment)
            setOnload(false)
        }
    }, [onload, loading, data])

    function updateApptStatus(status, desc, hide) {
        if (appt.status === status) {
            // status was not changed
            window.alert('Select a new updated status')
        } else {
            const mutations = {
                'cancel': apptCancel,
                'check_in': apptCheckIn,
                'left': apptLeft,
                'ignore': apptLeft,
                'no_show': apptNoShow,
                'start': apptStart,
                // 'undo': queueUndo
            }

            const mutation = mutations[status]

            if (mutation) {
                mutation({
                    variables: {
                        input: {
                            id: appt.id
                        }
                    }
                }).then(response => {
                    const key = Object.keys(response.data)[0]
                    const { errors, appointment: result } = response.data[key]

                    if (result) {
                        hide()

                        window.location.reload()
                    } else {
                        window.alert(errors[0].message)
                    }
                }).catch(error => {
                    window.alert(error.message)
                })
            }
        }
    }

    if (onload) return <LoadingPane />

    return (
        <div className='appointment-view full-screen-view'>
            <div className='toolbar'>
                <Breadcrumb
                    buttons={[{ title: 'Appointments', onClick: () => navigate('/dashboard/appointments') }, { title: appt.service.title }]}
                />

                {/* <IconButton
                    icon={require('../../../theme/assets/edit-icon-orange.png')}
                    onClick={() => setModal(true)}
                    title={{
                        color: '#E97100',
                        label: 'Update Status'
                    }}
                /> */}
            </div>

            <div className='sections-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Appointment Details</div>

                    <div className='form-frame'>
                        <HeaderValueLabel
                            header='Status'
                            value={<AppointmentStatus appointment={appt} />}
                        />

                        <HeaderValueLabel
                            header='Appointment Type'
                            value={appt.service.title}
                        />

                        <div className='header-value-label-view' />

                        <HeaderValueLabel
                            header='Date'
                            value={moment(appt.scheduledAtIso).format('ddd - MMM D, YYYY - h:mm A')}
                        />

                        <HeaderValueLabel
                            header='Member'
                            value={
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <Avatar width={22} user={appt.person} />
                                    <div style={{ marginLeft: 5 }}>{`${appt.person.firstName} ${appt.person.lastName}`}</div>
                                </div>
                            }
                        />

                        <HeaderValueLabel
                            header='Provider'
                            value={appt.provider ?
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <Avatar width={22} user={appt.provider} />
                                    <div style={{ marginLeft: 5 }}>{`${appt.provider.firstName} ${appt.provider.lastName}`}</div>
                                </div>
                                :
                                null
                            }
                        />
                    </div>
                </div>

                {appt.statusChanges?.length &&
                    <div className='section-frame'>
                        <div className='section-header'>Appointment Timeline</div>

                        <ul className='status-changes-list-view'>
                            {_.orderBy(appt.statusChanges, 'createdAtIso').map(statusChange => {
                                const { createdAtIso, id, toState, undoneByUser, user } = statusChange
                                const status = apptStatuses[toState]

                                return (
                                    <li className='status-change-cell' key={id}>
                                        <div className='left-section'>
                                            <div className='date-bubble-frame'>
                                                <div className='date-label'>{moment(createdAtIso).format('MMM D')}</div>
                                                <div className='date-sublabel'>{moment(createdAtIso).format('YYYY')}</div>
                                            </div>

                                            <div className='status-frame'>
                                                <div className='cell-label bold' style={{ color: status.color }}>{status.title}</div>
                                                {status?.desc && <div className='cell-label'>{status.desc}</div>}
                                            </div>
                                        </div>

                                        <div className='right-section'>
                                            {user &&
                                                <div className='flex-frame'>
                                                    <Avatar
                                                        width={22}
                                                        user={user}
                                                    />

                                                    <div className='cell-label'>{`${user.firstName} ${user.lastName} - ${moment(createdAtIso).format('MMM D, YYYY - h:mm A')}`}</div>
                                                </div>
                                            }

                                            {undoneByUser &&
                                                <div className='flex-frame undone'>
                                                    <div className='cell-label undone-label'>Undone By:</div>

                                                    <Avatar
                                                        width={22}
                                                        user={undoneByUser}
                                                    />

                                                    <div className='cell-label'>{`${undoneByUser.firstName} ${undoneByUser.lastName}`}</div>
                                                </div>
                                            }
                                        </div>

                                        <div className='separator' />
                                    </li>
                                )
                            })}
                        </ul>
                    </div>
                }
            </div>

            {modal &&
                <AppointmentStatusPane
                    appt={appt}
                    onHide={() => setModal(false)}
                    save={updateApptStatus}
                />
            }
        </div>
    )
}

export default Appointment