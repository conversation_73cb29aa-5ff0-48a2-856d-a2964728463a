import { gql } from '@apollo/client'

const OrgServicesWithNextAvailableAptQuery = gql`
    query OrgServicesWithNextAvailableApt {
        organizations {
            nodes {
                id      
                services {
                    id
                    title
                    desc
                    kind
                    params
                    nextAvailableAppt {
                        id
                        durationMins
                        startIso          
                    }
                }
            }
        }
    }
`

export default OrgServicesWithNextAvailableAptQuery
