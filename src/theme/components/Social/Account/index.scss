@import '../../../globals/Colors.scss';

.account-view {
    flex: 1 1;
    display: flex;
    flex-direction: column;

    .toolbar {
        padding: 26px 20px;
        box-sizing: border-box;
        height: 72px !important;
        box-shadow: none !important;

        .right-section {
            .sign-out-button {
                margin-left: 25px;
            }
        }
    }

    .content-frame {
        .account-details-frame {
            flex: 1 1;
            margin: 40px;
            display: flex;

            .right-section {
                flex: 1 1;
                margin-left: 80px;

                .header-frame {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .name {
                        font-size: 32px;
                        font-weight: 500;
                    }
                }

                .account-details-list-view {
                    columns: 2;
                    padding: 0px;
                    list-style: none;
                    margin: 48px 0px 0px 0px;

                    .account-details-cell {
                        display: flex;
                        margin-bottom: 6px;

                        .cell-label {
                            font-size: 15px;
                            font-weight: 400;
                        }

                        .key {
                            flex: 0 0 33%;
                        }

                        .value {
                            font-weight: 500;
                        }
                    }
                }
            }
        }

        .edit-account-details-frame {
            margin: 40px;
            display: flex;
            flex-direction: column;

            .section-header {
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }

            .form-frame {
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;

                .floating-text-field,
                .select-menu-view {
                    flex: 0 0 32%;
                    max-width: 400px;

                    .menu-label {
                        color: $teal;
                    }
                }
            }
        }
    }

    .password-update-section {
        margin: 40px;
        padding: 30px;
        border: 1px solid $border-grey;
        border-radius: 12px;
        background: white;

        .section-header {
            font-size: 21px;
            font-weight: 600;
            margin-bottom: 24px;
            color: $dark-text;
        }

        .password-form-frame {
            display: flex;
            flex-direction: column;
            gap: 16px;

            .password-buttons-frame {
                display: flex;
                gap: 12px;
                margin-top: 20px;
                justify-content: flex-start;
            }
        }
    }
}