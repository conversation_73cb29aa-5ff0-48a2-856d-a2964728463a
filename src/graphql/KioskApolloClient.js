import { ApolloClient, InMemoryCache } from '@apollo/client'

// Create a separate Apollo Client instance for kiosk authentication
// For login: don't send any auth headers
// For queries: use the kiosk token
const createKioskClient = (kioskToken = null) => {
    const clientConfig = {
        uri: process.env.REACT_APP_GRAPHQL_URL,
        cache: new InMemoryCache(),
        defaultOptions: {
            watchQuery: {
                fetchPolicy: 'no-cache',
                errorPolicy: 'ignore',
            },
            query: {
                fetchPolicy: 'no-cache',
                errorPolicy: 'all',
            }
        }
    }

    // Only add headers if we have a token (for queries, not login)
    if (kioskToken) {
        clientConfig.headers = { Authorization: kioskToken }
    }

    return new ApolloClient(clientConfig)
}

export default createKioskClient
