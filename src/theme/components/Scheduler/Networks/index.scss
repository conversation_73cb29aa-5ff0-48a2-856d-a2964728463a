@import '../../../globals/Colors.scss';

.networks-view {
    display: flex;
    flex-direction: column;

    .toolbar {
        display: flex;
        flex: 0 0 60px;
        padding: 0px 20px;
        align-items: center;
        box-sizing: border-box;
        justify-content: space-between;
        box-shadow: 0px 1px 0px 0px #E0E0E0;

        .toolbar-header {
            font-size: 21px;
            font-weight: 600;
        }
    }

    .content-frame {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .table-view {
        flex: 1 1;
        border: none;
        overflow: initial;

        .table-headers {
            border: none;
        }

        .hint-label {
            margin-top: 24px;
        }

        .table-data-cell {
            position: relative;
            border: none !important;
            overflow: hidden;

            .name-frame {
                display: flex;
                align-items: center;

                .org-name {
                    font-weight: 500;
                    font-size: 15px;
                    color: $dark-text;
                }
            }

            .services-frame {
                display: flex;
                flex-direction: column;
                align-items: flex-start;

                .services-count {
                    font-weight: 500;
                    font-size: 14px;
                    color: $teal;
                    margin-bottom: 4px;
                }

                .services-list {
                    font-size: 13px;
                    color: $grey;
                    line-height: 1.4; 
                    margin-top: 15px;
                    margin-left: 15px;                   
                }
            }
        }

        .table-data-cell:hover {
            background: #F9F9F9;
        }
    }
}
