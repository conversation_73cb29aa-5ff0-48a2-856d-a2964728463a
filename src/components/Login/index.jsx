import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@apollo/client'

import <PERSON><PERSON> from 'js-cookie'

import LoginMutation from '../../graphql/mutations/Login'

import FloatingTextField from '../Shared/FloatingTextField'
import LoadingPane from '../Shared/LoadingPane'

const Login = () => {
    const [loading, setLoading] = useState(false)
    const [email, setEmail] = useState('<EMAIL>')
    const [password, setPassword] = useState('Welcome2025!')
    const [error, setError] = useState('')

    const [login] = useMutation(LoginMutation)

    const navigate = useNavigate()
    const valid = (email && password)

    function handleLogin() {
        setLoading(true)

        login({
            variables: {
                input: {
                    email, password
                }
            }
        }).then(response => {
            const { errors, success, user } = response.data.login

            setLoading(false)

            if (success) {
                Cookie.set(process.env.REACT_APP_COOKIE_NAME, user.token)

                window.location.reload()
            } else {
                setError(errors[0].message)
            }
        }).catch(error => {
            console.log(error)
        })
    }

    return (
        <div className='login-view full-screen-view'>
            {loading && <LoadingPane />}

            <img
                className='wellup-logo'
                alt='wellup-logo'
                src={require('../../theme/assets/wellup-logo.png')}
            />

            <div className='centered-frame'>
                <div className='sign-in-label'>Sign In</div>

                <div className='form-frame'>
                    <FloatingTextField
                        label='Email'
                        icon={require('../../theme/assets/email-icon.png')}
                        iconSelected={require('../../theme/assets/email-icon-selected.png')}
                        onChange={text => setEmail(text)}
                        value={email}
                    />

                    <FloatingTextField
                        label='Password'
                        icon={require('../../theme/assets/password-icon.png')}
                        iconSelected={require('../../theme/assets/password-icon-selected.png')}
                        onChange={text => setPassword(text)}
                        type='password'
                        value={password}
                    />

                    {error && <div className='error-label'>{error}</div>}

                    <button
                        className={`sign-in-button ${valid ? '' : 'disabled'}`}
                        onClick={handleLogin}
                    >Sign In</button>
                </div>

                {/* <div className='subsection-frame'>
                    <div className='subsection-label'>Need help accessing your account.</div>
                    <button className='click-here-button' onClick={() => navigate('/forgot_password')}>Click here</button>
                </div> */}
            </div>
        </div>
    )
}

export default Login