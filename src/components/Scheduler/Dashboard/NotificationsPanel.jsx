import { useState } from 'react'
import moment from 'moment'

// TODO: Re-enable these imports when we have the correct GraphQL schema
// import { useQuery, useMutation } from '@apollo/client'
// import GetUserNotificationsQuery from '../../../graphql/queries/components/Scheduler/Dashboard/Notifications'
// import MarkNotificationReadMutation from '../../../graphql/mutations/Notification/MarkRead'
// import RemoveNotificationMutation from '../../../graphql/mutations/Notification/Remove'

import LoadingPane from '../../Shared/LoadingPane'
import IconButton from '../../Shared/IconButton'

const NotificationsPanel = ({ onClose }) => {
    const [notifications, setNotifications] = useState([])
    const [hasNextPage, setHasNextPage] = useState(false)

    // Temporary - disable the query until we have the correct schema
    const loading = false

    // Mock some sample notifications for demonstration
    useState(() => {
        setNotifications([
            {
                id: '1',
                subject: 'Welcome to WellUp',
                content: 'Thank you for joining our platform!',
                readAtIso: null
            },
            {
                id: '2',
                subject: 'Appointment Reminder',
                content: 'You have an appointment tomorrow at 2:00 PM',
                readAtIso: '2024-01-15T10:00:00Z'
            }
        ])
        setHasNextPage(false)
    }, [])

    // Temporary - disable mutations until we have the correct schema
    const markAsRead = () => Promise.resolve()
    const removeNotification = () => Promise.resolve()

    const handleMarkAsRead = async (notificationId) => {
        try {
            const response = await markAsRead({
                variables: { id: notificationId },
                optimisticResponse: {
                    notificationMarkRead: {
                        success: true,
                        result: {
                            id: notificationId,
                            readAtIso: new Date().toISOString(),
                            __typename: 'Notification'
                        },
                        errors: [],
                        __typename: 'NotificationMarkReadPayload'
                    }
                }
            })

            if (response.data.notificationMarkRead.success) {
                // Update local state
                setNotifications(prev => 
                    prev.map(notif => 
                        notif.id === notificationId 
                            ? { ...notif, readAtIso: response.data.notificationMarkRead.result.readAtIso }
                            : notif
                    )
                )
            } else {
                window.alert(response.data.notificationMarkRead.errors[0]?.message || 'Failed to mark as read')
            }
        } catch (error) {
            window.alert(error.message)
        }
    }

    const handleDelete = async (notificationId) => {
        try {
            const response = await removeNotification({
                variables: { id: notificationId },
                optimisticResponse: {
                    notificationRemove: {
                        success: true,
                        errors: [],
                        __typename: 'NotificationRemovePayload'
                    }
                }
            })

            if (response.data.notificationRemove.success) {
                // Update local state
                setNotifications(prev => prev.filter(notif => notif.id !== notificationId))
            } else {
                window.alert(response.data.notificationRemove.errors[0]?.message || 'Failed to delete notification')
            }
        } catch (error) {
            window.alert(error.message)
        }
    }

    const handleMarkAllAsRead = async () => {
        const unreadNotifications = notifications.filter(notif => !notif.readAtIso)
        
        try {
            await Promise.all(
                unreadNotifications.map(notif => 
                    markAsRead({ variables: { id: notif.id } })
                )
            )
            
            // Update local state
            setNotifications(prev => 
                prev.map(notif => ({ 
                    ...notif, 
                    readAtIso: notif.readAtIso || new Date().toISOString() 
                }))
            )
        } catch (error) {
            window.alert(error.message)
        }
    }

    const loadMore = async () => {
        // Temporary - disable load more until we have the correct schema
        console.log('Load more functionality will be implemented when GraphQL schema is available')
    }

    const unreadCount = notifications.filter(notif => !notif.readAtIso).length

    if (loading && notifications.length === 0) return <LoadingPane />

    return (
        <div className="notifications-panel">
            <div className="notifications-header">
                <h3>Notifications</h3>
                <div className="header-actions">
                    {unreadCount > 0 && (
                        <button 
                            className="mark-all-read-btn"
                            onClick={handleMarkAllAsRead}
                        >
                            Mark All as Read
                        </button>
                    )}
                    <IconButton
                        onClick={onClose}
                        title={{ label: 'Close' }}
                    />
                </div>
            </div>

            <div className="notifications-content">
                {notifications.length === 0 ? (
                    <div className="no-notifications">
                        No notifications to display
                    </div>
                ) : (
                    <>
                        <div className="notifications-list">
                            {notifications.map(notification => (
                                <NotificationItem
                                    key={notification.id}
                                    notification={notification}
                                    onMarkAsRead={handleMarkAsRead}
                                    onDelete={handleDelete}
                                />
                            ))}
                        </div>
                        
                        {hasNextPage && (
                            <button 
                                className="load-more-btn"
                                onClick={loadMore}
                                disabled={loading}
                            >
                                {loading ? 'Loading...' : 'Load More'}
                            </button>
                        )}
                    </>
                )}
            </div>
        </div>
    )
}

const NotificationItem = ({ notification, onMarkAsRead, onDelete }) => {
    const isUnread = !notification.readAtIso
    
    return (
        <div className={`notification-item ${isUnread ? 'unread' : 'read'}`}>
            <div className="notification-content">
                <div className="notification-subject">
                    {notification.subject}
                    {isUnread && <span className="unread-indicator">•</span>}
                </div>
                <div className="notification-body">
                    {notification.content}
                </div>
                {notification.readAtIso && (
                    <div className="notification-read-time">
                        Read {moment(notification.readAtIso).fromNow()}
                    </div>
                )}
            </div>
            
            <div className="notification-actions">
                {isUnread && (
                    <button 
                        className="mark-read-btn"
                        onClick={() => onMarkAsRead(notification.id)}
                        title="Mark as read"
                    >
                        ✓
                    </button>
                )}
                <button 
                    className="delete-btn"
                    onClick={() => onDelete(notification.id)}
                    title="Delete notification"
                >
                    ×
                </button>
            </div>
        </div>
    )
}

export default NotificationsPanel
