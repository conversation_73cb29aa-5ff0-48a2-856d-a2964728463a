@import '../../globals/Colors.scss';

.table-view {
    display: flex;
    overflow: hidden;
    border-radius: 8px;
    flex-direction: column;
    border: 1px solid #e5e7eb;
    background: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

    .table-headers {
        display: flex;
        margin: 0px;
        flex: 0 0 48px;
        list-style: none;
        padding: 0px 24px;
        align-items: center;
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;

        .table-header {
            color: #374151;
            display: flex;
            font-size: 12px;
            font-weight: 600;
            align-items: center;
            text-transform: uppercase;
            letter-spacing: 0.05em;

            .sort-icon {
                height: 12px;
                margin-left: 6px;
                opacity: 0.6;
            }
        }
    }

    .hint-label {
        display: flex;
        height: 60px;
        padding: 0px 24px;
        align-items: center;
        box-sizing: border-box;
        justify-content: center;
        color: #6b7280;
        font-size: 14px;
    }

    .table-data-list-view {
        margin: 0px;
        padding: 0px;
        list-style: none;

        .table-data-cell {
            min-height: 40px;
            display: flex;
            padding: 16px 24px;
            align-items: flex-start;
            box-sizing: border-box;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.15s ease;

            &:hover {
                background-color: #f9fafb;
            }

            .cell-label {
                font-size: 14px;
                font-weight: 400;
                color: #111827;
                line-height: 1.5;

                .address-item, .phone-item {
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }

                .org-name {
                    font-weight: 600;
                    color: #111827;
                }

                .org-desc {
                    color: #6b7280;
                    line-height: 1.4;
                }
            }
        }

        .table-data-cell:last-child {
            border: none;
        }
    }
}