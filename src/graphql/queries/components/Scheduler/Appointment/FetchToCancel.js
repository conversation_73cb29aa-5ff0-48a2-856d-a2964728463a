import { gql } from "@apollo/client"

const FetchAppointmentToCancelQuery = gql`
    query FetchAppointmentToCancel($orgId: ID!, $appointmentId: ID!) {
        org(id: $orgId) {
            name
            addresses {
                address
            }
            appointment(id: $appointmentId) {
                id
                scheduledAtIso
                provider {
                    fullName
                }
                service {
                    title          
                }
            }
        }
    }
`

export default FetchAppointmentToCancelQuery
