import { gql } from "@apollo/client"

const GetUserNotificationsQuery = gql`
    query GetUserNotifications($first: Int, $after: String) {
        me {
            notifications(first: $first, after: $after) {
                edges {
                    node {
                        id
                        subject
                        content
                        readAtIso
                    }
                }
                pageInfo {
                    endCursor
                    hasNextPage
                }
            }
        }
    }
`

export default GetUserNotificationsQuery
