import { gql } from '@apollo/client'

export const USER_FRAGMENT = gql`
    fragment UserFragment on User {
        id
        attachments(labels: ["profile_picture"]) {
            id
            label
            contentUrl
        }
        addresses {
            id
            label
            street
            city
            state
            zip
        }
        dob
        firstName
        lastName
        mainLanguage
        disabilities
        emails {
            id
            label
            address
        }
        latestData(metrics: ["person.gender", "person.ethnicity"]) {
            id
            key
            values
        }
        phones {
            id
            label
            number
        }
        preferences {
            id
            key
            value
        }
        roles {
            id
            role
            org {
                id
            }
        }
    }
`