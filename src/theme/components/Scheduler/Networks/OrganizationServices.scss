.organization-services-view {
    width: 100%;

    .organization-header {
        margin-bottom: 24px;

        h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #111827;
        }
    }
    
    .error-message, .no-services {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: #6b7280;
        font-size: 16px;
        text-align: center;
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        margin: 24px 0;
    }
    
    .error-message {
        color: #dc2626;
        background: #fef2f2;
        border-color: #fecaca;
    }
    
    .services-list {
        display: flex;
        flex-direction: column;
        gap: 1px;
        width: 100%;
        margin: 15px;

        .service-item {
            background: white;
            border: 1px solid #e5e7eb;
            padding: 20px 24px;
            transition: all 0.15s ease;
            cursor: pointer;
            width: 100%;
            box-sizing: border-box;

            &:first-child {
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
            }

            &:last-child {
                border-bottom-left-radius: 8px;
                border-bottom-right-radius: 8px;
            }

            &:not(:last-child) {
                border-bottom: none;
            }

            &:hover {
                background-color: #f9fafb;
                border-color: #ff8c00;
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(255, 140, 0, 0.15);
            }

            .service-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;

                .service-title {
                    margin: 0;
                    font-size: 18px;
                    font-weight: 600;
                    color: #111827;
                    line-height: 1.3;
                }

                .service-kind {
                    background-color: #ff8c00;
                    color: white;
                    padding: 4px 12px;
                    border-radius: 8px;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    white-space: nowrap;
                }
            }

            .service-description {
                margin: 0 0 12px 0;
                color: #6b7280;
                line-height: 1.5;
                font-size: 14px;
            }

            .appointment-info {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 16px;
                font-size: 14px;

                .appointment-details {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    flex: 1;
                }

                .appointment-label {
                    color: #6b7280;
                    font-weight: 500;
                }

                .appointment-time {
                    color: #ff8c00;
                    font-weight: 600;
                }

                .no-appointment {
                    color: #6b7280;
                    font-style: italic;
                }

                .book-appointment-btn {
                    background: linear-gradient(180deg, #FD8205 0%, #E97100 100%);
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 6px;
                    font-size: 13px;
                    font-weight: 600;
                    cursor: pointer;
                    transition: all 0.15s ease;
                    white-space: nowrap;

                    &:hover {
                        background: linear-gradient(180deg, #E97100 0%, #D66600 100%);
                        transform: translateY(-1px);
                        box-shadow: 0 2px 8px rgba(233, 113, 0, 0.3);
                    }

                    &:active {
                        transform: translateY(0);
                    }
                }
            }
        }
    }

    // Responsive design
    @media (max-width: 768px) {
        .services-list {
            .service-item {
                padding: 16px 20px;

                .service-header {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 8px;
                    margin-bottom: 8px;
                }

                .appointment-info {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 12px;

                    .appointment-details {
                        flex-direction: column;
                        align-items: flex-start;
                        gap: 4px;
                    }

                    .book-appointment-btn {
                        align-self: stretch;
                        text-align: center;
                    }
                }
            }
        }

        .organization-header {
            h2 {
                font-size: 20px;
            }
        }
    }
}
