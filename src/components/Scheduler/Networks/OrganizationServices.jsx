import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router"

import OrgServicesWithNextAvailableAptQuery from "../../../graphql/queries/components/Scheduler/Organizations/OrgServicesWithNextAvailableApt"
import createKioskClient from "../../../graphql/KioskApolloClient"
import useKioskAuth from "../../../hooks/useKioskAuth"

import LoadingPane from "../../Shared/LoadingPane"
import Breadcrumb from "../../Shared/Breadcrumb"

import "../../../theme/components/Scheduler/Networks/OrganizationServices.scss"

const OrganizationServices = () => {
    const { orgId } = useParams()
    const navigate = useNavigate()
    
    const [organization, setOrganization] = useState(null)
    const [services, setServices] = useState([])
    const [loading, setLoading] = useState(true)
    const [error, setError] = useState(null)

    const { loading: authLoading, error: authError, authenticateKiosk } = useKioskAuth()

    // Handle service click - navigate to appointment creation
    const handleServiceClick = (service) => {
        navigate(`/dashboard/appointments/create/${orgId}/${service.id}`)
    }

    // Fetch organization services
    useEffect(() => {
        if (orgId) {
            const fetchOrganizationServices = async () => {
                try {
                    setLoading(true)
                    setError(null)
                    
                    // Authenticate with kiosk user
                    const token = await authenticateKiosk()
                    
                    // Create kiosk client with the token for queries
                    const kioskClient = createKioskClient(token)
                    
                    // Fetch organization services using kiosk authentication
                    const response = await kioskClient.query({
                        query: OrgServicesWithNextAvailableAptQuery
                    })
                    
                    if (response.data?.organizations?.nodes) {
                        // Find the selected organization and its services
                        const selectedOrg = response.data.organizations.nodes.find(org => org.id === orgId)
                        if (selectedOrg) {
                            setOrganization(selectedOrg)
                            setServices(selectedOrg.services || [])
                        } else {
                            setError('Organization not found')
                        }
                    }
                } catch (error) {
                    console.error('Failed to fetch organization services:', error)
                    setError(error.message || 'Failed to load services')
                } finally {
                    setLoading(false)
                }
            }

            fetchOrganizationServices()
        }
    }, [orgId, authenticateKiosk])

    const formatDateTime = (isoString) => {
        if (!isoString) return 'Not available'
        try {
            const date = new Date(isoString)
            const weekday = date.toLocaleDateString('en-US', { weekday: 'long' })
            const month = date.toLocaleDateString('en-US', { month: 'short' })
            const day = date.getDate()
            const year = date.getFullYear()
            const time = date.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            })
            return `${weekday}, ${month} ${day}, ${year} at ${time}`
        } catch (e) {
            return 'Invalid date'
        }
    }



    const generateBreadcrumb = () => {
        return [
            { 
                title: 'Networks', 
                onClick: () => navigate('/dashboard/networks') 
            },
            { 
                title: organization?.name || 'Organization Services'
            }
        ]
    }

    if (loading || authLoading) return <LoadingPane />

    // Show error if kiosk authentication failed
    if (authError) {
        return (
            <div className='organization-services-view full-screen-view'>
                <div className='toolbar'>
                    <Breadcrumb buttons={generateBreadcrumb()} />
                </div>
                <div className='content-frame'>
                    <div className="error-message">
                        Failed to authenticate with kiosk user: {authError}
                    </div>
                </div>
            </div>
        )
    }

    if (error) {
        return (
            <div className='organization-services-view full-screen-view'>
                <div className='toolbar'>
                    <Breadcrumb buttons={generateBreadcrumb()} />
                </div>
                <div className='content-frame'>
                    <div className="error-message">
                        Error loading services: {error}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className='organization-services-view full-screen-view'>
            <div className='toolbar'>
                <Breadcrumb buttons={generateBreadcrumb()} />
            </div>

            <div className='content-frame'>
                <div className="organization-header">
                    <h2>{organization?.name}</h2>
                </div>

                {services.length === 0 ? (
                    <div className="no-services">
                        No services available for this organization.
                    </div>
                ) : (
                    <div className="services-list">
                        {services.map(service => (
                            <div
                                key={service.id}
                                className="service-item clickable"
                                onClick={() => handleServiceClick(service)}
                            >
                                <div className="service-header">
                                    <h3 className="service-title">{service.title}</h3>                                    
                                </div>

                                {service.desc && (
                                    <p className="service-description">{service.desc}</p>
                                )}

                                <div className="appointment-info">
                                    <div className="appointment-details">
                                        <span className="appointment-label">Next Available Appointment:</span>
                                        {service.nextAvailableAppt && service.nextAvailableAppt.length > 0 ? (
                                            <span className="appointment-time">
                                                {service.nextAvailableAppt
                                                    .map(appt => formatDateTime(appt.startIso))
                                                    .join(', ')
                                                }
                                            </span>
                                        ) : (
                                            <span className="no-appointment">No available appointments</span>
                                        )}
                                    </div>
                                    <button
                                        className="book-appointment-btn"
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            handleServiceClick(service)
                                        }}
                                    >
                                        Book Appointment
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        </div>
    )
}

export default OrganizationServices
