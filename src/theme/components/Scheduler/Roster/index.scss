@import '../../../globals/Colors.scss';

.setup-roster-view {
    .toolbar {
        padding: 0px;
    }

    .table-view {
        .table-data-list-view {
            .table-data-cell {
                .name-frame {
                    display: flex;
                    align-items: center;

                    .avatar {
                        flex: 0 0 32px;
                    }
                }

                .full-name {
                    margin-left: 12px;
                }

                .actions-frame {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 8px;

                    .edit-button {
                        padding: 6px 12px;
                        background: linear-gradient(180deg, #008390 0%, #006B75 100%);
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;

                        &:hover {
                            background: linear-gradient(180deg, #006B75 0%, #005A63 100%);
                            transform: translateY(-1px);
                        }
                    }

                    .delete-button {
                        padding: 6px 12px;
                        background: linear-gradient(180deg, #E42B57 0%, #C41E3A 100%);
                        color: white;
                        border: none;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        cursor: pointer;
                        transition: all 0.2s ease;

                        &:hover {
                            background: linear-gradient(180deg, #C41E3A 0%, #A01729 100%);
                            transform: translateY(-1px);
                        }
                    }

                    .kiosk-user-label {
                        padding: 6px 12px;
                        background: #f8f9fa;
                        color: #6c757d;
                        border: 1px solid #dee2e6;
                        border-radius: 4px;
                        font-size: 12px;
                        font-weight: 500;
                        text-align: center;
                        cursor: help;
                    }
                }
            }
        }
    }

    .hint-frame {
        display: flex;
        font-size: 15px;
        font-weight: 400;

        .hint-button {
            font-size: 15px;
            color: $orange;
            border: none;
            margin-left: 5px;
            background: none;
        }
    }
}