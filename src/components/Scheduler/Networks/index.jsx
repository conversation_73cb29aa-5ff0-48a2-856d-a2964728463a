import { useEffect, useState } from "react"
import { useNavigate } from "react-router"

import OrganizationsQuery from "../../../graphql/queries/components/Scheduler/Organizations"
import createKioskClient from "../../../graphql/KioskApolloClient"
import useKioskAuth from "../../../hooks/useKioskAuth"

import LoadingPane from "../../Shared/LoadingPane"
import TableView from "../../Shared/TableView"

const SchedulerNetworks = () => {
    const [onload, setOnload] = useState(true)
    const [organizations, setOrganizations] = useState([])
    const [queryLoading, setQueryLoading] = useState(false)

    const navigate = useNavigate()
    const { loading: authLoading, error: authError, authenticateKiosk } = useKioskAuth()

    // Handle organization row click - navigate to services page
    const handleOrganizationClick = (organization) => {
        navigate(`/dashboard/networks/${organization.id}/services`)
    }

    // Authenticate with kiosk user and fetch organizations
    useEffect(() => {
        if (onload) {
            const fetchOrganizations = async () => {
                try {
                    setQueryLoading(true)

                    // Authenticate with kiosk user (no token sent for login)
                    const token = await authenticateKiosk()

                    // Create kiosk client with the token for queries
                    const kioskClient = createKioskClient(token)

                    // Fetch organizations using kiosk authentication
                    const response = await kioskClient.query({
                        query: OrganizationsQuery
                    })

                    if (response.data?.organizations?.nodes) {
                        setOrganizations(response.data.organizations.nodes)
                    }
                } catch (error) {
                    console.error('Failed to fetch organizations:', error)
                    // You might want to show an error message to the user here
                } finally {
                    setQueryLoading(false)
                    setOnload(false)
                }
            }

            fetchOrganizations()
        }
    }, [onload, authenticateKiosk])

    // Format organizations data for table display
    const formattedOrganizations = organizations.map(org => {
        return {
            id: org.id,
            name: org.name,
            nameFrame: (
                <div className='name-frame'>
                    <div className='org-name'>{org.name}</div>
                </div>
            ),
            addressFrame: (
                <div className='address-frame'>
                    {org.addresses && org.addresses.length > 0 ? (
                        org.addresses.map((addr, index) => (
                            <div key={index} className='address-item'>{addr.address}</div>
                        ))
                    ) : (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No address</span>
                    )}
                </div>
            ),
            phoneFrame: (
                <div className='phone-frame'>
                    {org.phones && org.phones.length > 0 ? (
                        org.phones.map((phone, index) => (
                            <div key={index} className='phone-item'>{phone.number}</div>
                        ))
                    ) : (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No phone</span>
                    )}
                </div>
            ),
            descFrame: (
                <div className='desc-frame'>
                    {org.desc ? (
                        <div className='org-desc'>{org.desc}</div>
                    ) : (
                        <span style={{ color: '#999', fontStyle: 'italic' }}>No description</span>
                    )}
                </div>
            )
        }
    })

    if (onload || authLoading || queryLoading) return <LoadingPane />

    // Show error if kiosk authentication failed
    if (authError) {
        return (
            <div className='networks-view full-screen-view'>
                <div className='toolbar'>
                    <div className='toolbar-header'>Networks - Authentication Error</div>
                </div>
                <div className='content-frame'>
                    <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        height: '200px',
                        color: '#666',
                        fontStyle: 'italic'
                    }}>
                        Failed to authenticate with kiosk user: {authError}
                    </div>
                </div>
            </div>
        )
    }

    return (
        <div className='networks-view full-screen-view'>
            <div className='toolbar'>
                <div className='toolbar-header'>{`Networks (${organizations.length})`}</div>
            </div>

            <div className='content-frame'>
                <TableView
                    data={formattedOrganizations}
                    headers={[
                        { title: 'Name', width: '25%' },
                        { title: 'Address', width: '30%' },
                        { title: 'Phone', width: '20%' },
                        { title: 'Description', width: '25%' }
                    ]}
                    keys={['nameFrame', 'addressFrame', 'phoneFrame', 'descFrame']}
                    onCellClick={handleOrganizationClick}
                />
            </div>
        </div>
    )
}

export default SchedulerNetworks
