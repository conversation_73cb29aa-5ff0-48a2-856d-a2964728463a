import { useCallback, useEffect, useState } from 'react'
import { useNavigate, useParams } from 'react-router'

import moment from 'moment'
import { useLazyQuery, useMutation, useQuery } from '@apollo/client'

import AppointmentCreateQuery from '../../../graphql/queries/components/Scheduler/Appointment/Create'
import AppointmentCreateUsersQuery from '../../../graphql/queries/components/Scheduler/Appointment/CreateUsers'
import ProviderTimesQuery from '../../../graphql/queries/components/Scheduler/Appointment/ProviderTimes'
import ApptCreateMutation from '../../../graphql/mutations/Appointment/Create'
import ApptBookMutation from '../../../graphql/mutations/Appointment/Book'

import { generateDaysOfWeek } from '../../../helpers/Functions'
import useCurrentRole from '../../../hooks/useCurrentRole'

import RosterUsersPane from '../Roster/panes/Users'

import Avatar from '../../Shared/Avatar'
import Breadcrumb from '../../Shared/Breadcrumb'
import DateRangePicker from '../../Shared/DateRangePicker'
import IconButton from '../../Shared/IconButton'
import LoadingPane from '../../Shared/LoadingPane'
import SelectMenu from '../../Shared/SelectMenu'
import SegmentedButton from '../../Shared/SegmentedButton'
import Checkbox from '../../Shared/Checkbox'

const AppointmentCreate = () => {
    const { currentRole } = useCurrentRole()
    const navigate = useNavigate()
    const { orgId, serviceId } = useParams()

    const [onload, setOnload] = useState(true)
    const [modal, setModal] = useState(false)
    const [org, setOrg] = useState()
    const [member, setMember] = useState()
    const [service, setService] = useState()
    const [provider, setProvider] = useState()
    const [reason, setReason] = useState('')
    const [dateButtons, setDateButtons] = useState(generateDaysOfWeek())
    const [times, setTimes] = useState([])
    const [time, setTime] = useState()

    const [apptCreate, { loading: requestLoading }] = useMutation(ApptCreateMutation)
    const [apptBook, { loading: requestLoading2 }] = useMutation(ApptBookMutation)

    // Use orgId from URL params if available, otherwise use current role org
    const targetOrgId = orgId || currentRole?.org?.id

    // Query for services/providers from target org
    const { data: orgData, loading: orgLoading } = useQuery(AppointmentCreateQuery, {
        variables: { id: targetOrgId },
        skip: !targetOrgId
    })    

    // Query for members from current user's org (always use currentRole org for members)
    const { data: usersData, loading: usersLoading } = useQuery(AppointmentCreateUsersQuery, {
        variables: { id: currentRole?.org?.id },
        skip: !currentRole?.org?.id
    })
        
    const [providerTimes] = useLazyQuery(ProviderTimesQuery)

    const valid = (service && member && provider)

    // Combine loading states
    const loading = orgLoading || usersLoading

    useEffect(() => {
        if (onload && !loading && orgData?.org) {

            // Create combined org object with users from the separate query
            const combinedOrg = {
                ...orgData.org,
                users: {
                    nodes: usersData?.org?.users?.nodes || []
                }
            }

            setOrg(combinedOrg)

            // Pre-select service if serviceId is provided in URL
            if (serviceId && orgData.org.services) {
                const preSelectedService = orgData.org.services.find(s => s.id === serviceId)
                if (preSelectedService) {
                    setService(preSelectedService)
                }
            }

            setOnload(false)
        }
    }, [onload, loading, orgData, usersData, serviceId])

    // fetch day appointment times
    useEffect(() => {
        if (valid && targetOrgId) {
            const selected = dateButtons.find(o => o.selected)
            const options = {
                org: targetOrgId,
                service: service.id,
                date: selected.date.format('MM/DD/YYYY'),
            }

            // TO-DO: throwing 500
            // if (provider !== 'next') options.provider = provider

            providerTimes({
                variables: options
            }).then(response => {
                setTimes(response.data.org.service.availableTimesIso ?? [])
            }).catch(error => {
                window.alert(error.message)
            })
        }
    }, [valid, dateButtons, targetOrgId, service, providerTimes])

    function handleDateSelected(d) {
        const buttons = generateDaysOfWeek(d)
        const formattedDateButtons = buttons.map(b => {
            return {
                ...b,
                selected: b.date.startOf('day').isSame(d.startOf('day'))
            }
        })

        setDateButtons(formattedDateButtons)
    }

    function handleSegmentedButton(button) {
        setDateButtons(dateButtons.map(o => ({ ...o, selected: o.date === button.date })))
    }

    const bookAppt = useCallback((apptId) => {
        apptBook({
            variables: {
                input: {
                    id: apptId
                }
            }
        }).then(response => {
            const { errors, appointment } = response.data.apptBook

            if (appointment) {
                navigate('/dashboard/appointments?notify=1')
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }, [apptBook, navigate])

    const save = useCallback(() => {
        if (!valid || !time || !targetOrgId) {
            window.alert('Missing fields required')
            return
        }

        const data = {
            org: targetOrgId,
            service: service.id,
            person: member.id,
            kind: 'appointment',
            reason,
            provider,
            startEpoch: parseInt(moment(time).valueOf() / 1000),
            endEpoch: parseInt(moment(time).add(service.durationMins, 'minutes').valueOf() / 1000)
        }

        if (provider !== 'next') data.provider = provider

        apptCreate({
            variables: {
                input: data
            }
        }).then(response => {
            const { errors, appointment } = response.data.apptCreate

            if (appointment) {
                bookAppt(appointment.id)
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }, [valid, time, targetOrgId, service, member, reason, provider, apptCreate, bookAppt])

    if (onload || requestLoading || requestLoading2 || !targetOrgId) return <LoadingPane />

    return (
        <div className='appointment-create-view full-screen-view'>
            <div className='toolbar'>
                <Breadcrumb
                    buttons={[
                        {
                            title: orgId ? orgData.org.name : 'Appointments',
                            onClick: () => navigate(orgId ? '/dashboard/networks' : '/dashboard/appointments')
                        },
                        { title: orgId ? 'Book Network Appointment' : 'Add Scheduled Appointment' }
                    ]}
                />

                <div className='button-frame'>
                    <IconButton
                        onClick={() => navigate(orgId ? `/dashboard/networks/${orgId}/services` : '/dashboard/appointments')}
                        title={{ label: 'Cancel' }}
                    />
                    <IconButton
                        className={valid ? '' : 'disabled'}
                        icon={require('../../../theme/assets/check-icon-white.png')}
                        iconHeight={9.17}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={save}
                        title={{
                            color: 'white',
                            label: 'Save'
                        }}
                    />
                </div>
            </div>

            <div className='content-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Appointment Details</div>

                    <div className='form-frame'>
                        <SelectMenu
                            options={org.services.filter(s => s.kind === 'appointment' && s.status === 'active').map(s => ({ id: s.id, value: s.title }))}
                            onChange={value => setService(org.services.find(s => s.id === value))}
                            placeholder='Select appointment type'
                            value={service?.id}
                        />

                        {member ?
                            <div className='member-frame'>
                                <div className='header'>Member</div>

                                <div className='member-details-frame'>
                                    <Avatar user={member} width={22} />

                                    <div className='name'>{`${member.firstName} ${member.lastName} - `}</div>

                                    <button className='select-member-button' onClick={() => setModal(true)}>Select Member</button>
                                </div>
                            </div>
                            :
                            <div className='member-hint-frame clickable' onClick={() => setModal(true)}>
                                <div className='hint-label'>Select Member</div>

                                <img
                                    alt='search-icon'
                                    className='search-icon'
                                    src={require('../../../theme/assets/search-icon-orange.png')}
                                />
                            </div>
                        }

                        <SelectMenu
                            options={[
                                { id: 'next', value: 'Next Available' },
                                ...org.providers.nodes.map(u => ({ id: u.id, value: `${u.firstName} ${u.lastName}` }))
                            ]}
                            onChange={value => setProvider(value)}
                            placeholder='Select Provider'
                            value={provider}
                        />

                        <textarea
                            className='form-textarea'
                            placeholder='Enter additional information...'
                            onChange={e => setReason(e.target.value)}
                            value={reason}
                        />
                    </div>
                </div>

                <div className='section-frame'>
                    <div className='section-header'>Dates and Time</div>

                    {valid ?
                        <div className='available-times-frame'>
                            <div className='top-section'>
                                <DateRangePicker
                                    onChange={d => handleDateSelected(d)}
                                    value={dateButtons.find(o => o.selected)?.date.toISOString()}
                                />

                                <SegmentedButton
                                    buttons={dateButtons}
                                    onClick={button => handleSegmentedButton(button)}
                                />
                            </div>

                            {times?.length ?
                                <ul className='times-list-view'>
                                    {times.map(t => {
                                        const startTime = moment(t).format('h:mmA')
                                        const endTime = moment(t).add(service.durationMins, 'minutes').format('h:mmA')
                                        const selected = (time === t)

                                        return (
                                            <li
                                                key={t}
                                                className='time-cell'
                                            >
                                                <Checkbox
                                                    // readOnly={true}
                                                    onChange={() => setTime(t)}
                                                    title={`${startTime}-${endTime}`}
                                                    value={selected}
                                                />
                                            </li>
                                        )
                                    })}
                                </ul>
                                :
                                <div className='hint-label'>There are no appointment times to display for this day.</div>
                            }
                        </div>
                        :
                        <div className='hint-label'>Add information above to see available appointment slots</div>
                    }
                </div>
            </div>

            {modal &&
                <RosterUsersPane
                    onChange={user => setMember(user)}
                    onHide={() => setModal(false)}
                    users={org.users.nodes}
                />
            }
        </div>
    )
}

export default AppointmentCreate