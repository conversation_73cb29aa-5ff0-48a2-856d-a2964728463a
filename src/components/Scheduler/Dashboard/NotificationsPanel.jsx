import { useState } from 'react'
import { useQuery, useMutation } from '@apollo/client'
import moment from 'moment'

import GetUserNotificationsQuery from '../../../graphql/queries/components/Scheduler/Dashboard/Notifications'
import MarkNotificationReadMutation from '../../../graphql/mutations/Notification/MarkRead'
import RemoveNotificationMutation from '../../../graphql/mutations/Notification/Remove'

import LoadingPane from '../../Shared/LoadingPane'
import IconButton from '../../Shared/IconButton'

const NotificationsPanel = ({ onClose }) => {
    const [notifications, setNotifications] = useState([])
    const [hasNextPage, setHasNextPage] = useState(false)
    const [endCursor, setEndCursor] = useState(null)

    const { loading, fetchMore } = useQuery(GetUserNotificationsQuery, {
        variables: { first: 10 },
        onCompleted: (data) => {
            if (data?.me?.notifications) {
                setNotifications(data.me.notifications.edges.map(edge => edge.node))
                setHasNextPage(data.me.notifications.pageInfo.hasNextPage)
                setEndCursor(data.me.notifications.pageInfo.endCursor)
            }
        }
    })

    const [markAsRead] = useMutation(MarkNotificationReadMutation)
    const [removeNotification] = useMutation(RemoveNotificationMutation)

    const handleMarkAsRead = async (notificationId) => {
        try {
            const response = await markAsRead({
                variables: { id: notificationId },
                optimisticResponse: {
                    notificationMarkRead: {
                        success: true,
                        result: {
                            id: notificationId,
                            readAtIso: new Date().toISOString(),
                            __typename: 'Notification'
                        },
                        errors: [],
                        __typename: 'NotificationMarkReadPayload'
                    }
                }
            })

            if (response.data.notificationMarkRead.success) {
                // Update local state
                setNotifications(prev => 
                    prev.map(notif => 
                        notif.id === notificationId 
                            ? { ...notif, readAtIso: response.data.notificationMarkRead.result.readAtIso }
                            : notif
                    )
                )
            } else {
                window.alert(response.data.notificationMarkRead.errors[0]?.message || 'Failed to mark as read')
            }
        } catch (error) {
            window.alert(error.message)
        }
    }

    const handleDelete = async (notificationId) => {
        try {
            const response = await removeNotification({
                variables: { id: notificationId },
                optimisticResponse: {
                    notificationRemove: {
                        success: true,
                        errors: [],
                        __typename: 'NotificationRemovePayload'
                    }
                }
            })

            if (response.data.notificationRemove.success) {
                // Update local state
                setNotifications(prev => prev.filter(notif => notif.id !== notificationId))
            } else {
                window.alert(response.data.notificationRemove.errors[0]?.message || 'Failed to delete notification')
            }
        } catch (error) {
            window.alert(error.message)
        }
    }

    const handleMarkAllAsRead = async () => {
        const unreadNotifications = notifications.filter(notif => !notif.readAtIso)
        
        try {
            await Promise.all(
                unreadNotifications.map(notif => 
                    markAsRead({ variables: { id: notif.id } })
                )
            )
            
            // Update local state
            setNotifications(prev => 
                prev.map(notif => ({ 
                    ...notif, 
                    readAtIso: notif.readAtIso || new Date().toISOString() 
                }))
            )
        } catch (error) {
            window.alert(error.message)
        }
    }

    const loadMore = async () => {
        if (hasNextPage && endCursor) {
            try {
                const result = await fetchMore({
                    variables: { first: 10, after: endCursor }
                })
                
                if (result.data?.me?.notifications) {
                    const newNotifications = result.data.me.notifications.edges.map(edge => edge.node)
                    setNotifications(prev => [...prev, ...newNotifications])
                    setHasNextPage(result.data.me.notifications.pageInfo.hasNextPage)
                    setEndCursor(result.data.me.notifications.pageInfo.endCursor)
                }
            } catch (error) {
                window.alert(error.message)
            }
        }
    }

    const unreadCount = notifications.filter(notif => !notif.readAtIso).length

    if (loading && notifications.length === 0) return <LoadingPane />

    return (
        <div className="notifications-panel">
            <div className="notifications-header">
                <h3>Notifications</h3>
                <div className="header-actions">
                    {unreadCount > 0 && (
                        <button 
                            className="mark-all-read-btn"
                            onClick={handleMarkAllAsRead}
                        >
                            Mark All as Read
                        </button>
                    )}
                    <IconButton
                        onClick={onClose}
                        title={{ label: 'Close' }}
                    />
                </div>
            </div>

            <div className="notifications-content">
                {notifications.length === 0 ? (
                    <div className="no-notifications">
                        No notifications to display
                    </div>
                ) : (
                    <>
                        <div className="notifications-list">
                            {notifications.map(notification => (
                                <NotificationItem
                                    key={notification.id}
                                    notification={notification}
                                    onMarkAsRead={handleMarkAsRead}
                                    onDelete={handleDelete}
                                />
                            ))}
                        </div>
                        
                        {hasNextPage && (
                            <button 
                                className="load-more-btn"
                                onClick={loadMore}
                                disabled={loading}
                            >
                                {loading ? 'Loading...' : 'Load More'}
                            </button>
                        )}
                    </>
                )}
            </div>
        </div>
    )
}

const NotificationItem = ({ notification, onMarkAsRead, onDelete }) => {
    const isUnread = !notification.readAtIso
    
    return (
        <div className={`notification-item ${isUnread ? 'unread' : 'read'}`}>
            <div className="notification-content">
                <div className="notification-subject">
                    {notification.subject}
                    {isUnread && <span className="unread-indicator">•</span>}
                </div>
                <div className="notification-body">
                    {notification.content}
                </div>
                {notification.readAtIso && (
                    <div className="notification-read-time">
                        Read {moment(notification.readAtIso).fromNow()}
                    </div>
                )}
            </div>
            
            <div className="notification-actions">
                {isUnread && (
                    <button 
                        className="mark-read-btn"
                        onClick={() => onMarkAsRead(notification.id)}
                        title="Mark as read"
                    >
                        ✓
                    </button>
                )}
                <button 
                    className="delete-btn"
                    onClick={() => onDelete(notification.id)}
                    title="Delete notification"
                >
                    ×
                </button>
            </div>
        </div>
    )
}

export default NotificationsPanel
