.notifications-container {
    position: relative;
    display: inline-block;
}

.notifications-button {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 8px;
    transition: background-color 0.15s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
        background-color: #f3f4f6;
    }

    .notification-icon {
        font-size: 20px;
        line-height: 1;
    }

    .notification-badge {
        position: absolute;
        top: 2px;
        right: 2px;
        background: #ef4444;
        color: white;
        border-radius: 10px;
        padding: 2px 6px;
        font-size: 11px;
        font-weight: 600;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 1;
    }
}

.notifications-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    align-items: flex-start;
    justify-content: flex-end;
    padding: 60px 20px 20px 20px;

    .notifications-backdrop {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
        cursor: pointer;
    }
}

.notifications-panel {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    width: 400px;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    z-index: 1001;

    .notifications-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20px 24px 16px 24px;
        border-bottom: 1px solid #e5e7eb;

        h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #111827;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 12px;

            .mark-all-read-btn {
                background: none;
                border: none;
                color: #ff8c00;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                padding: 4px 8px;
                border-radius: 4px;
                transition: background-color 0.15s ease;

                &:hover {
                    background-color: #fff7ed;
                }
            }
        }
    }

    .notifications-content {
        flex: 1;
        overflow-y: auto;
        max-height: 500px;

        .no-notifications {
            padding: 40px 24px;
            text-align: center;
            color: #6b7280;
            font-size: 14px;
        }

        .notifications-list {
            padding: 8px 0;
        }

        .load-more-btn {
            width: 100%;
            padding: 12px 24px;
            background: none;
            border: none;
            border-top: 1px solid #e5e7eb;
            color: #ff8c00;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.15s ease;

            &:hover:not(:disabled) {
                background-color: #fff7ed;
            }

            &:disabled {
                color: #9ca3af;
                cursor: not-allowed;
            }
        }
    }
}

.notification-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 16px 24px;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.15s ease;

    &:hover {
        background-color: #f9fafb;
    }

    &.unread {
        background-color: #fff7ed;
        border-left: 3px solid #ff8c00;

        &:hover {
            background-color: #fef3e2;
        }
    }

    &.read {
        opacity: 0.8;
    }

    .notification-content {
        flex: 1;
        min-width: 0;

        .notification-subject {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            font-size: 14px;
            color: #111827;
            margin-bottom: 4px;
            line-height: 1.4;

            .unread-indicator {
                color: #ff8c00;
                font-size: 16px;
                line-height: 1;
            }
        }

        .notification-body {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
            margin-bottom: 4px;
            word-wrap: break-word;
        }

        .notification-read-time {
            font-size: 11px;
            color: #9ca3af;
            font-style: italic;
        }
    }

    .notification-actions {
        display: flex;
        align-items: flex-start;
        gap: 4px;
        flex-shrink: 0;

        .mark-read-btn,
        .delete-btn {
            background: none;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            line-height: 1;
            transition: all 0.15s ease;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mark-read-btn {
            color: #10b981;

            &:hover {
                background-color: #ecfdf5;
                color: #059669;
            }
        }

        .delete-btn {
            color: #ef4444;
            font-size: 16px;

            &:hover {
                background-color: #fef2f2;
                color: #dc2626;
            }
        }
    }
}

// Responsive design
@media (max-width: 768px) {
    .notifications-overlay {
        padding: 60px 10px 10px 10px;
    }

    .notifications-panel {
        width: 100%;
        max-width: 400px;
        max-height: 70vh;
    }

    .notification-item {
        padding: 12px 16px;

        .notification-content {
            .notification-subject {
                font-size: 13px;
            }

            .notification-body {
                font-size: 12px;
            }
        }
    }
}
