@import '../../../globals/Colors.scss';

.scheduler-setup-details-view {
    position: relative;

    .button-frame {
        position: absolute;
        right: 0px;
        top: -72px;
        display: flex;

        .icon-button-view {
            margin-left: 12px;
        }
    }

    .section-frame {
        margin-bottom: 48px;

        .section-header-frame {
            height: 40px;
            display: flex;
            margin-bottom: 24px;
            align-items: center;
            justify-content: space-between;

            .section-header {
                font-weight: 600;
                font-size: 18px;
            }

            .add-button {
                font-size: 15px;
                font-weight: 500;
                color: $orange;
                border: none;
                background: none;
            }
        }

        .form-frame {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;

            .header-value-label-view,
            .floating-text-field,
            .select-menu-view {
                flex: 0 0 32%;
                margin-bottom: 16px;

                .floating-label {
                    color: $grey;
                }
            }
        }

        .insurances-list-view {
            margin: 0px;
            padding: 0px;
            list-style: none;

            .insurance-cell {
                margin-bottom: 24px;

                .cell-label {
                    font-size: 15px;
                    line-height: 19.5px;
                }

                .type {
                    color: $grey;
                }
            }
        }

        .table-view {
            .actions-frame {
                display: flex;
                align-items: center;
                justify-content: center;

                .delete-button {
                    padding: 6px 12px;
                    background: linear-gradient(180deg, #E42B57 0%, #C41E3A 100%);
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 12px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &:hover {
                        background: linear-gradient(180deg, #C41E3A 0%, #A01729 100%);
                        transform: translateY(-1px);
                    }
                }
            }
        }

        .closed-dates-list-view {
            margin: 0px;
            padding: 0px;
            list-style: none;

            .closed-date-cell {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 12px 0px;
                border-bottom: 1px solid #E8E8E8;

                .date-label {
                    font-size: 15px;
                    line-height: 19.5px;
                    font-weight: 500;
                }

                .remove-date-button {
                    padding: 4px 8px;
                    background: linear-gradient(180deg, #E42B57 0%, #C41E3A 100%);
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 11px;
                    font-weight: 500;
                    cursor: pointer;
                    transition: all 0.2s ease;

                    &:hover {
                        background: linear-gradient(180deg, #C41E3A 0%, #A01729 100%);
                        transform: translateY(-1px);
                    }
                }
            }
        }
    }
}