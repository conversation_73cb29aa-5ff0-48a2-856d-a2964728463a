const NOTIFICATION_API_URL = 'https://wellup-notification-proxy-780af391600a.herokuapp.com'
const SERVICE_TOKEN = '7f3e1d8c9a254d6e9b4f0c22d5f7a89bbf3c5d1a7e4f9c8d6b2a4f1c3d5e7a9f'

export const sendStaffInviteEmail = async (staffData) => {
    const emailData = {
        to: staffData.email,
        subject: `Welcome to ${staffData.organizationName} - Your Account Details`,
        html: `
            <html>
                <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #CF6000; margin: 0;">Welcome to ${staffData.organizationName}</h1>
                    </div>
                    
                    <p style="font-size: 16px; line-height: 1.5;">Dear ${staffData.firstName} ${staffData.lastName},</p>
                    
                    <p style="font-size: 16px; line-height: 1.5;">
                        You have been added as a <strong>${staffData.role}</strong> at ${staffData.organizationName}.
                    </p>
                    
                    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #CF6000;">
                        <h3 style="color: #CF6000; margin-top: 0;">Your Login Credentials:</h3>
                        <p style="margin: 10px 0;"><strong>Email:</strong> ${staffData.email}</p>
                        <p style="margin: 10px 0;"><strong>Temporary Password:</strong> Welcome2025!</p>
                    </div>
                    
                    <p style="font-size: 16px; line-height: 1.5;">
                        Please log in at <a href="${window.location.origin}" style="color: #CF6000; text-decoration: none;">${window.location.origin}</a> and change your password after your first login.
                    </p>
                    
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 0;">
                            Best regards,<br>
                            <strong>${staffData.organizationName} Team</strong>
                        </p>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 15px; background-color: #f3f4f6; border-radius: 5px; font-size: 12px; color: #6b7280;">
                        <p style="margin: 0;">This is an automated message. Please do not reply to this email.</p>
                    </div>
                </body>
            </html>
        `,
        text: `Welcome to ${staffData.organizationName}! You have been added as a ${staffData.role}. Your login credentials: Email: ${staffData.email}, Temporary Password: Welcome2025! Please log in at ${window.location.origin} and change your password.`,
        from: "Wellup <<EMAIL>>"
    }

    try {
        const response = await fetch(`${NOTIFICATION_API_URL}/v1/email/send`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SERVICE_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(emailData)
        })

        if (response.ok) {
            return { success: true, message: 'Staff invite email sent successfully' }
        } else {
            const errorData = await response.json()
            console.error('Notification API Error:', errorData)
            throw new Error(errorData.message || 'Failed to send staff invite email')
        }
    } catch (error) {
        console.error('Staff invite email error:', error)
        throw error
    }
}

export const sendPasswordUpdateConfirmation = async (userData) => {
    const emailData = {
        to: userData.email,
        subject: "Password Updated Successfully - Wellup",
        html: `
            <html>
                <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #CF6000; margin: 0;">Password Updated Successfully</h1>
                    </div>
                    
                    <p style="font-size: 16px; line-height: 1.5;">Dear ${userData.firstName} ${userData.lastName},</p>
                    
                    <p style="font-size: 16px; line-height: 1.5;">
                        Your password has been successfully updated on ${new Date().toLocaleDateString()}.
                    </p>
                    
                    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #CF6000;">
                        <h3 style="color: #CF6000; margin-top: 0;">Security Notice:</h3>
                        <p style="margin: 10px 0;">If you did not make this change, please contact our support team immediately.</p>
                    </div>
                    
                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 0;">
                            Best regards,<br>
                            <strong>Wellup Team</strong>
                        </p>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 15px; background-color: #f3f4f6; border-radius: 5px; font-size: 12px; color: #6b7280;">
                        <p style="margin: 0;">This is an automated security notification.</p>
                    </div>
                </body>
            </html>
        `,
        text: `Your password has been successfully updated on ${new Date().toLocaleDateString()}. If you did not make this change, please contact support immediately.`,
        from: "Wellup <<EMAIL>>",
        replyTo: "<EMAIL>"
    }

    try {
        const response = await fetch(`${NOTIFICATION_API_URL}/v1/email/send`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SERVICE_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(emailData)
        })

        if (response.ok) {
            return { success: true, message: 'Password update confirmation sent successfully' }
        } else {
            const errorData = await response.json()
            console.error('Notification API Error:', errorData)
            throw new Error(errorData.message || 'Failed to send password update confirmation')
        }
    } catch (error) {
        console.error('Password update confirmation error:', error)
        throw error
    }
}

export const sendPasswordResetEmail = async (userData, resetToken) => {
    const resetUrl = `${window.location.origin}/reset-password?token=${resetToken}`

    const emailData = {
        to: userData.email,
        subject: "Reset Your Wellup Password",
        html: `
            <html>
                <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                    <div style="text-align: center; margin-bottom: 30px;">
                        <h1 style="color: #CF6000; margin: 0;">Password Reset Request</h1>
                    </div>

                    <p style="font-size: 16px; line-height: 1.5;">Dear ${userData.firstName} ${userData.lastName},</p>

                    <p style="font-size: 16px; line-height: 1.5;">
                        You requested to reset your password. Click the link below to reset your password:
                    </p>

                    <div style="text-align: center; margin: 30px 0;">
                        <a href="${resetUrl}" style="background-color: #CF6000; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 500;">Reset Password</a>
                    </div>

                    <div style="background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #CF6000;">
                        <h3 style="color: #CF6000; margin-top: 0;">Important:</h3>
                        <p style="margin: 10px 0;">This link expires in 1 hour.</p>
                        <p style="margin: 10px 0;">If you did not request this reset, please ignore this email.</p>
                    </div>

                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
                        <p style="font-size: 16px; line-height: 1.5; margin-bottom: 0;">
                            Best regards,<br>
                            <strong>Wellup Team</strong>
                        </p>
                    </div>
                </body>
            </html>
        `,
        text: `You requested to reset your password. Visit this link to reset: ${resetUrl}. This link expires in 1 hour. If you did not request this reset, please ignore this email.`,
        from: "Wellup <<EMAIL>>",
        replyTo: "<EMAIL>"
    }

    try {
        const response = await fetch(`${NOTIFICATION_API_URL}/v1/email/send`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SERVICE_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(emailData)
        })

        if (response.ok) {
            return { success: true, message: 'Password reset email sent successfully' }
        } else {
            const errorData = await response.json()
            console.error('Notification API Error:', errorData)
            throw new Error(errorData.message || 'Failed to send password reset email')
        }
    } catch (error) {
        console.error('Password reset email error:', error)
        throw error
    }
}

export const sendSMS = async (phoneNumber, message) => {
    // Validate phone number format (basic validation)
    if (!phoneNumber || !phoneNumber.trim()) {
        throw new Error('Phone number is required')
    }

    // Ensure phone number starts with + for international format
    let formattedPhone = phoneNumber.trim()
    if (!formattedPhone.startsWith('+')) {
        // Assume US number if no country code
        formattedPhone = `+1${formattedPhone.replace(/\D/g, '')}`
    }

    // Validate message
    if (!message || !message.trim()) {
        throw new Error('Message content is required')
    }

    const smsData = {
        to: formattedPhone,
        message: message.trim(),
        fromId: "default"
    }

    try {
        const response = await fetch(`${NOTIFICATION_API_URL}/v1/sms/send`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SERVICE_TOKEN}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(smsData)
        })

        if (response.ok) {
            const responseData = await response.json()
            return { success: true, message: 'SMS sent successfully', data: responseData }
        } else {
            const errorData = await response.json()
            console.error('SMS API Error:', errorData)
            throw new Error(errorData.message || 'Failed to send SMS')
        }
    } catch (error) {
        console.error('SMS send error:', error)
        throw error
    }
}
