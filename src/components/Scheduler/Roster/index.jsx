import { useEffect, useState } from 'react'

import _ from 'lodash'
import { useQuery } from '@apollo/client'
import useCurrentRole from '../../../hooks/useCurrentRole'

import SchedulerRosterQuery from '../../../graphql/queries/components/Scheduler/Roster'

import Avatar from '../../Shared/Avatar'
import LoadingPane from '../../Shared/LoadingPane'
import IconButton from '../../Shared/IconButton'
import TableView from '../../Shared/TableView'

const SetupRoster = (props) => {
    const { navigate } = props
    const [onload, setOnload] = useState(true)
    const [users, setUsers] = useState([])
    const { currentRole } = useCurrentRole()

    const { data, loading } = useQuery(SchedulerRosterQuery, {
        variables: { id: currentRole?.org?.id },
        skip: !currentRole?.org?.id
    })

    useEffect(() => {
        if (onload && !loading) {
            setUsers(data.org.users.nodes.filter(u => !u.roles.find(r => r.role === 'person')).map(u => {
                const email = u.emails.find(o => o.label === 'main')
                const phone = u.phones.find(o => o.label === 'main')
                const role = u.roles.find(o => o.org?.id === currentRole?.org?.id)

                return {
                    ...u,
                    fullName: `${u.firstName} ${u.lastName}`,
                    email: email?.address ?? '-',
                    phone: phone?.number ?? '-',
                    role: _.startCase(role.role)
                }
            }))
            setOnload(false)
        }
    }, [onload, loading, data])

    function goToStaffRoute(id) {
        navigate(`/dashboard/setup/roster/${id}`)
    }

    if (onload || !currentRole?.org?.id) return <LoadingPane />

    return (
        <div className='setup-roster-view'>
            <div className='toolbar' style={{ padding: 0 }}>
                <div className='toolbar-header'>{`Staff (${users.length})`}</div>

                <div className='right-section'>
                    <IconButton
                        icon={require('../../../theme/assets/plus-icon-white.png')}
                        iconHeight={12}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={() => goToStaffRoute('add')}
                        title={{
                            color: 'white',
                            label: 'Add Staff'
                        }}
                    />
                </div>
            </div>

            {users?.length ?
                <TableView
                    headers={[
                        { title: 'Name', width: '33%' },
                        { title: 'Role', width: '33%' },
                        { title: 'Email', width: '33%' },
                        { title: 'Phone', width: '33%' },
                    ]}
                    data={users.map(user => {
                        return {
                            ...user,
                            nameFrame: {
                                sort: 'fullName',
                                component: <div className='name-frame'>
                                    <Avatar
                                        width={32}
                                        user={user}
                                    />

                                    <div className='cell-label full-name ellipsis'>{user.fullName}</div>
                                </div>
                            }
                        }
                    })}
                    keys={['nameFrame', 'role', 'email', 'phone']}
                    onCellClick={(user) => goToStaffRoute(user.id)}
                />
                :
                <div className='hint-frame'>
                    No staff members have been added.
                    <button className='hint-button' onClick={() => goToStaffRoute('add')}>+ Add Staff Member</button>
                </div>
            }
        </div>
    )
}

export default SetupRoster