import { useEffect, useState } from 'react'
import { useMutation } from '@apollo/client'

import OrgUpdateMutation from '../../../graphql/mutations/Org/Update'

import useConstants from '../../../hooks/useConstants'

import DateTextField from '../../Shared/DateTextField'
import FloatingTextField from '../../Shared/FloatingTextField'

import HeaderValueLabel from '../../Shared/HeaderValueLabel'
import IconButton from '../../Shared/IconButton'
import LoadingPane from '../../Shared/LoadingPane'
import SelectMenu from '../../Shared/SelectMenu'
import SetupInsurancesPane from './panes/Insurances'
import TableView from '../../Shared/TableView'

const SetupDetails = (props) => {
    const { edit, org, setEdit, setOrg } = props
    const { insurances, orgKinds, timezones, usaStates } = useConstants()

    // internal component state for editing
    const [organization, setOrganization] = useState()
    const [modal, setModal] = useState(false)

    const address = org.addresses.find(o => o.label === 'main')
    const phone = org.phones.find(o => o.label === 'main')
    const supportedIns = org?.preferences?.find(o => o.key === 'insurances_list')
    const timezone = org?.preferences?.find(o => o.key === 'timezone')
    const closedDays = org?.preferences?.find(o => o.key === 'closed_days')

    const [orgUpdate, { loading }] = useMutation(OrgUpdateMutation)

    useEffect(() => {
        parseOrg(org)
    }, [org])

    function parseOrg(o) {
        // Handle insurances: if [""] then treat as empty, otherwise use the values
        const insuranceValues = supportedIns?.value?.[0] === "" ? [] : (supportedIns?.value ?? [])

        const orgData = {
            ...o,
            ...address,
            id: o.id,
            phone: phone?.number,
            insurances: insuranceValues,
            timezone: timezone?.value?.[0] || 'America/New_York', // Default to Eastern Time
            closedDates: closedDays?.value || [] // Array of ISO date strings
        }
        setOrganization(orgData)
    }

    function handleChange(key, value) {
        setOrganization({
            ...organization,
            [key]: value
        })
    }

    function handleEdit() {
        setEdit(true)
        parseOrg(org)
    }

    function handleRemoveInsurance(insuranceId) {
        const updatedInsurances = organization.insurances.filter(id => parseInt(id) !== insuranceId)
        // If all insurances are removed, send [""] to signal backend to clear the preference
        const finalInsurances = updatedInsurances.length === 0 ? [""] : updatedInsurances
        setOrganization({
            ...organization,
            insurances: finalInsurances
        })
    }

    function handleAddClosedDate(date) {
        const isoDate = date.toISOString().split('T')[0] // Convert to YYYY-MM-DD format
        const updatedDates = [...(organization.closedDates || []), isoDate]
        setOrganization({
            ...organization,
            closedDates: updatedDates
        })
    }

    function handleRemoveClosedDate(dateToRemove) {
        const updatedDates = organization.closedDates.filter(date => date !== dateToRemove)
        setOrganization({
            ...organization,
            closedDates: updatedDates
        })
    }

    function save() {
        const { kind, id, insurances, name, phone, street, street2, city, state, zip, timezone, closedDates } = organization
        const data = {
            id,
            kind,
            name
        }

        if (phone) data.phones = [{ number: phone }]
        if (street && city && state && zip) data.addresses = [{ street, street2, city, state, zip }]

        // Add closed dates to the mutation data
        if (closedDates?.length) {
            data.closedDates = closedDates
        }

        // Build preferences array
        const preferences = []
        console.log(insurances)
        if (insurances?.length) {
            // Handle case where insurances is [""] (signal to clear) or has actual values
            const insuranceValues = insurances[0] === "" ? [""] : insurances.map(i => i.toString())
            preferences.push({ key: 'insurances_list', value: insuranceValues })
        }
        if (timezone) {
            preferences.push({ key: 'timezone', value: [timezone] })
        }
        if (closedDates) {
            preferences.push({ key: 'closed_days', value: closedDates })
        }
        if (preferences.length) {
            data.preferences = preferences
        }

        orgUpdate({
            variables: {
                input: data
            }
        }).then(response => {
            const { errors, organization: result } = response.data.orgUpdate

            if (result) {
                setOrg(result)
                setEdit(false)
            } else {
                window.alert(errors[0].message)
            }
        }).catch(error => {
            window.alert(error.message)
        })
    }

    if (loading || !organization) return <LoadingPane />

    return (
        <div className='scheduler-setup-details-view'>
            {edit &&
                <div className='floating-button-frame'>
                    <IconButton
                        onClick={() => setEdit(false)}
                        title={{
                            color: '#747A7A',
                            label: 'Cancel'
                        }}
                    />

                    <IconButton
                        icon={require('../../../theme/assets/check-icon-white.png')}
                        iconHeight={9.17}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={save}
                        title={{
                            color: 'white',
                            label: 'Save'
                        }}
                    />
                </div>
            }

            <div className='section-frame'>
                <div className='section-header-frame'>
                    <div className='section-header'>Network Details</div>

                    {!edit &&
                        <IconButton
                            icon={require('../../../theme/assets/edit-icon-orange.png')}
                            onClick={handleEdit}
                            title={{
                                color: '#E97100',
                                label: 'Update Details'
                            }}
                        />
                    }
                </div>

                {edit ?
                    <div className='form-frame'>
                        <FloatingTextField
                            label='Network title'
                            onChange={text => handleChange('name', text)}
                            value={organization?.name}
                        />

                        <SelectMenu
                            label='Network type'
                            options={orgKinds}
                            onChange={value => handleChange('kind', value)}
                            placeholder='Select network type'
                            value={organization.kind}
                        />

                        <div className='floating-text-field' />

                        <FloatingTextField
                            label='Street 1'
                            onChange={text => handleChange('street', text)}
                            value={organization.street}
                        />

                        <FloatingTextField
                            label='Street 2'
                            onChange={text => handleChange('street2', text)}
                            value={organization.street2}
                        />

                        <FloatingTextField
                            label='City'
                            onChange={text => handleChange('city', text)}
                            value={organization.city}
                        />

                        <SelectMenu
                            label='State'
                            options={usaStates}
                            onChange={value => handleChange('state', value)}
                            placeholder='Select state'
                            value={organization.state}
                        />

                        <FloatingTextField
                            label='Zip code'
                            maxLength={6}
                            onChange={text => handleChange('zip', text)}
                            value={organization.zip}
                        />

                        <FloatingTextField
                            label='Phone'
                            onChange={text => handleChange('phone', text)}
                            type='tel'
                            value={organization.phone}
                        />

                        <SelectMenu
                            label='Timezone'
                            options={timezones}
                            onChange={value => handleChange('timezone', value)}
                            placeholder='Select timezone'
                            value={organization.timezone}
                        />
                    </div>
                    :
                    <div className='form-frame'>
                        <HeaderValueLabel
                            header='Title of Network'
                            value={org.name}
                        />

                        <HeaderValueLabel
                            header='Network type'
                            value={orgKinds.find(o => o.id === org.kind).value}
                        />

                        <HeaderValueLabel
                            header='Address'
                            value={address?.address}
                        />

                        <HeaderValueLabel
                            header='Contact Phone'
                            value={phone?.number}
                        />

                        <HeaderValueLabel
                            header='Timezone'
                            value={timezone?.value?.[0] ? timezones.find(tz => tz.id === timezone.value[0])?.value : 'Eastern Time (New York)'}
                        />
                    </div>
                }
            </div>

            <div className='section-frame'>
                <div className='section-header-frame'>
                    <div className='section-header'>Accepted Insurances</div>

                    {edit &&
                        <button className='add-button' onClick={() => setModal(true)}>+ Add Insurance</button>
                    }
                </div>

                {edit ?
                    <TableView
                        headers={[
                            { title: 'Carrier', width: '40%' },
                            { title: 'Plan Type', width: '40%' },
                            { title: 'Actions', width: '20%' },
                        ]}
                        data={organization.insurances?.filter(i => i !== "").map(i => {
                            const insurance = insurances.find(o => o.id === parseInt(i))
                            if (!insurance) return null // Skip if insurance not found

                            return {
                                ...insurance,
                                actionsFrame: (
                                    <div className='actions-frame'>
                                        <button
                                            className='delete-button'
                                            onClick={(e) => {
                                                e.stopPropagation()
                                                handleRemoveInsurance(insurance.id)
                                            }}
                                            title="Remove insurance"
                                        >
                                            Remove
                                        </button>
                                    </div>
                                )
                            }
                        }).filter(Boolean) ?? []}
                        keys={['carrier', 'type', 'actionsFrame']}
                    />
                    :
                    <div>
                        {supportedIns && supportedIns.value?.[0] !== "" ?
                            <ul className='insurances-list-view'>
                                {supportedIns?.value?.filter(ins => ins !== "").map(ins => {
                                    const insurance = insurances.find(o => o.id === parseInt(ins))
                                    if (!insurance) return null // Skip if insurance not found

                                    return (
                                        <li className='insurance-cell' key={insurance.id}>
                                            <div className='cell-label carrier'>{insurance?.carrier}</div>
                                            <div className='cell-label type'>{insurance?.type}</div>
                                        </li>
                                    )
                                }).filter(Boolean)}
                            </ul>
                            :
                            <div className='hint-label'>There are no insurances to display.</div>
                        }
                    </div>
                }
            </div>

            <div className='section-frame'>
                <div className='section-header-frame'>
                    <div className='section-header'>Closed Days</div>

                    {edit &&
                        <DateTextField
                            label='Add Closed Date'
                            placeholder='Select a date to add'
                            onChange={(date) => {
                                if (date) {
                                    handleAddClosedDate(new Date(date))
                                }
                            }}
                            value={null} // Always reset after selection
                            minDate={new Date()} // Prevent selecting past dates
                            excludeDates={organization.closedDates?.map(dateStr => new Date(dateStr)) || []} // Prevent duplicates
                            clearAfterSelect={true} // Clear picker after selection for multi-select behavior
                        />
                    }
                </div>

                {edit ?
                    <div>
                        {organization.closedDates?.length ?
                            <ul className='closed-dates-list-view'>
                                {organization.closedDates.map(dateStr => {
                                    const date = new Date(dateStr)
                                    const formattedDate = date.toLocaleDateString('en-US', {
                                        weekday: 'long',
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                    })

                                    return (
                                        <li className='closed-date-cell' key={dateStr}>
                                            <div className='date-label'>{formattedDate}</div>
                                            <button
                                                className='remove-date-button'
                                                onClick={() => handleRemoveClosedDate(dateStr)}
                                                title="Remove closed date"
                                            >
                                                Remove
                                            </button>
                                        </li>
                                    )
                                })}
                            </ul>
                            :
                            <div className='hint-label'>No closed days have been added.</div>
                        }
                    </div>
                    :
                    <div>
                        {organization.closedDates?.length ?
                            <ul className='closed-dates-list-view'>
                                {organization.closedDates.map(dateStr => {
                                    const date = new Date(dateStr)
                                    const formattedDate = date.toLocaleDateString('en-US', {
                                        weekday: 'long',
                                        year: 'numeric',
                                        month: 'long',
                                        day: 'numeric'
                                    })

                                    return (
                                        <li className='closed-date-cell' key={dateStr}>
                                            <div className='date-label'>{formattedDate}</div>
                                        </li>
                                    )
                                })}
                            </ul>
                            :
                            <div className='hint-label'>No closed days have been set.</div>
                        }
                    </div>
                }
            </div>

            {modal && organization &&
                <SetupInsurancesPane
                    handleChange={handleChange}
                    onHide={() => setModal(false)}
                    insurances={organization.insurances}
                />
            }
        </div>
    )
}

export default SetupDetails