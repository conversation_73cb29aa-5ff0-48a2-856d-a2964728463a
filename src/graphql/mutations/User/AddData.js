import { gql } from '@apollo/client'
import { USER_FRAGMENT } from '../../fragments/User'

const UserAddDataMutation = gql`
    mutation UserAddDataMutation($input: UserAddDataInput!) {
        userAddData(input: $input) {
            errors {
                message
            }
            success
            user {
                ...UserFragment
            }
        }
    }
    ${USER_FRAGMENT}
`

export default UserAddDataMutation
