import { useState, useCallback } from 'react'

import KioskLoginMutation from '../graphql/mutations/Auth/KioskLogin'
import createKioskClient from '../graphql/KioskApolloClient'

const useKioskAuth = () => {
    const [kioskToken, setKioskToken] = useState(null)
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)

    const authenticateKiosk = useCallback(async () => {
        setLoading(true)
        setError(null)

        const kioskEmail = process.env.REACT_APP_KIOSK_EMAIL
        const kioskPassword = process.env.REACT_APP_KIOSK_PASSWORD

        if (!kioskEmail || !kioskPassword) {
            const errorMessage = 'Kiosk credentials not found in environment variables'
            setError(errorMessage)
            setLoading(false)
            throw new Error(errorMessage)
        }

        try {
            // Create a client without auth headers for login
            const loginClient = createKioskClient()

            const response = await loginClient.mutate({
                mutation: KioskLoginMutation,
                variables: {
                    input: {
                        email: kioskEmail,
                        password: kioskPassword
                    }
                }
            })

            const { errors, success, user } = response.data.login

            if (success && user?.token) {
                setKioskToken(user.token)
                return user.token
            } else {
                const errorMessage = errors?.[0]?.message || 'Kiosk authentication failed'
                setError(errorMessage)
                throw new Error(errorMessage)
            }
        } catch (err) {
            const errorMessage = err.message || 'Kiosk authentication error'
            setError(errorMessage)
            console.error('Kiosk authentication error:', err)
            throw err
        } finally {
            setLoading(false)
        }
    }, [])

    const clearKioskToken = useCallback(() => {
        setKioskToken(null)
        setError(null)
    }, [])

    return {
        kioskToken,
        loading,
        error,
        authenticateKiosk,
        clearKioskToken
    }
}

export default useKioskAuth
