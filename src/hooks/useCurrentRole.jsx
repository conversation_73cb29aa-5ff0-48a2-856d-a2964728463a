import { useContext } from 'react'
import { RoleContext } from '../context/Role'

const useCurrentRole = () => {
    const { role } = useContext(RoleContext)

    // Add null safety for role parsing
    let currentRole = null
    if (role) {
        try {
            currentRole = (typeof role === 'object') ? role : JSON.parse(role)
        } catch (error) {
            console.error('Error parsing role:', error)
            currentRole = null
        }
    }

    return {
        currentRole
    }
}

export default useCurrentRole