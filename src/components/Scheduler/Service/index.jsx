import { useEffect, useState } from "react"
import { useLazyQuery, useMutation, useQuery } from "@apollo/client"

import _ from 'lodash'
import moment from "moment"

import SchedulerServiceQuery from "../../../graphql/queries/components/Scheduler/Service"
import SchedulerRosterQuery from "../../../graphql/queries/components/Scheduler/Roster"

import ServiceCreateMutation from "../../../graphql/mutations/Service/Create"
import ServiceUpdateMutation from "../../../graphql/mutations/Service/Update"
import ServiceActivateMutation from "../../../graphql/mutations/Service/Activate"
import ServiceDayScheduleCreateMutation from "../../../graphql/mutations/Service/DaySchedule/Create"
import ServiceDayScheduleRemoveMutation from "../../../graphql/mutations/Service/DaySchedule/Remove"
import ServiceDayScheduleUpdateMutation from "../../../graphql/mutations/Service/DaySchedule/Update"
import ServiceProviderAddMutation from '../../../graphql/mutations/Service/Provider/Add'
import ServiceProviderRemoveMutation from '../../../graphql/mutations/Service/Provider/Remove'

import useConstants from "../../../hooks/useConstants"

import { generateOptions } from '../../../helpers/Functions'

import ProvidersPane from "./panes/Providers"

import Avatar from "../../Shared/Avatar"
import DaySchedules from "../../Shared/DaySchedules"
import FloatingTextField from "../../Shared/FloatingTextField"
import IconButton from "../../Shared/IconButton"
import LoadingPane from "../../Shared/LoadingPane"
import SelectMenu from "../../Shared/SelectMenu"
import TableView from "../../Shared/TableView"

const SetupService = (props) => {
    const { edit, navigate, org, params, setEdit } = props

    // hooks
    const { serviceKinds } = useConstants()

    const [onload, setOnload] = useState(true)
    const [requesting, setRequesting] = useState(false)
    const [modal, setModal] = useState(false)
    const [service, setService] = useState()

    const { data: rosterData, loading: rosterLoading } = useQuery(SchedulerRosterQuery, {
        variables: {
            id: org.id,
            role: 'provider'
        }
    }) // used for providers pane
    const [getService, { data, loading }] = useLazyQuery(SchedulerServiceQuery)

    const [serviceCreate] = useMutation(ServiceCreateMutation)
    const [serviceUpdate] = useMutation(ServiceUpdateMutation)
    const [serviceActivate] = useMutation(ServiceActivateMutation)
    const [serviceDayScheduleCreate] = useMutation(ServiceDayScheduleCreateMutation)
    const [serviceDayScheduleRemove] = useMutation(ServiceDayScheduleRemoveMutation)
    const [serviceDayScheduleUpdate] = useMutation(ServiceDayScheduleUpdateMutation)
    const [serviceProviderAdd] = useMutation(ServiceProviderAddMutation)
    const [serviceProviderRemove] = useMutation(ServiceProviderRemoveMutation)

    const valid = (service?.title)
    const providers = service?.providers ?? []

    useEffect(() => {
        setEdit(true)

        if (params.id !== 'add') {
            // service exists - fetch data
            getService({
                variables: {
                    id: org.id,
                    service: params.id
                }
            }).then(response => {
                const service = response.data.org.service

                setService({
                    ...service,
                    providers: service?.providers ?
                        service.providers.map(p => p.id) : [],
                    daySchedules: service?.daySchedules ?
                        service.daySchedules.map(dS => ({
                            ...dS,
                            dayStr: dS.dayName,
                            startTime: moment(dS.startTime, 'hmm'),
                            endTime: moment(dS.endTime, 'hmm')
                        })) : [],
                })

                setOnload(false)
            }).catch(error => {
                window.alert(error.message)
            })
        } else {
            setOnload(false)
        }
    }, [])

    function addProvidersToService(arr = [], i, serviceId, isNewService = false) {
        const providerIds = data?.org ? data.org.service.providers.map(p => p.id) : []

        if (i === arr.length) {
            const providersToRemove = providerIds.filter(pId => !arr.includes(pId))

            removeProvidersFromService(providersToRemove, 0, serviceId, isNewService)
        } else {
            const existing = providerIds.includes(arr[i])
            const onSuccess = () => addProvidersToService(arr, i + 1, serviceId, isNewService)

            // check if provider already on service
            if (existing) {
                // skip
                console.log('provider already exists -- skipping')
                onSuccess()
            } else {
                serviceProviderAdd({
                    variables: {
                        input: {
                            service: serviceId,
                            provider: arr[i]
                        }
                    }
                }).then(response => {
                    const { errors, success } = response.data.serviceProviderAdd

                    if (success) {
                        onSuccess()
                    } else {
                        setRequesting(false)
                        window.alert(errors[0].message)
                    }
                }).catch(error => {
                    setRequesting(false)
                    window.alert(error.message)
                })
            }
        }

    }

    function cancel() {
        setEdit(false)
        navigate('/dashboard/setup/services')
    }

    function formatUser(u) {
        const email = u.emails.find(o => o.label === 'main')
        const phone = u.phones.find(o => o.label === 'main')
        const gender = u.latestData.find(o => o.key === 'person.gender')

        return {
            ...u,
            fullName: `${u.firstName} ${u.lastName}`,
            email: email?.address,
            phone: phone?.number,
            gender: _.startCase(gender.values[0])
        }
    }

    function handleChange(key, value) {
        setService({
            ...service,
            [key]: value
        })
    }

    function goToServicesRoute() {
        setEdit(false)
        navigate(`/dashboard/setup/services`)
    }

    async function activateService(serviceId) {
        try {
            const response = await serviceActivate({
                variables: {
                    input: { id: serviceId }
                }
            })

            const { errors, success } = response.data.serviceActivate

            if (success) {
                goToServicesRoute()
            } else {
                setRequesting(false)
                console.error('Service activation failed:', errors)
                window.alert(errors[0].message)
            }
        } catch (error) {
            setRequesting(false)
            console.error('Service activation error:', error)
            window.alert(error?.message || 'Failed to activate service')
        }
    }

    function removeProvidersFromService(arr, i, serviceId, isNewService = false) {
        if (i === arr.length) {
            // All providers processed - activate if new service, otherwise just navigate
            if (isNewService) {
                activateService(serviceId)
            } else {
                setRequesting(false)
                goToServicesRoute()
            }
        } else {
            serviceProviderRemove({
                variables: {
                    input: {
                        service: serviceId,
                        provider: arr[i]
                    }
                }
            }).then(response => {
                const { errors, success } = response.data.serviceProviderRemove

                if (success) {
                    // to-do: handle outstanding daySchedules attached to service
                    removeProvidersFromService(arr, i + 1, serviceId, isNewService)
                } else {
                    setRequesting(false)
                    window.alert(errors[0].message)
                }
            }).catch(error => {
                setRequesting(false)
                window.alert(error.message)
            })
        }

    }

    function save() {
        const { daySchedules, desc, durationMins, kind, providers, id, title } = service
        const mutation = id ? serviceUpdate : serviceCreate
        const data = {
            desc,
            durationMins: parseInt(durationMins),
            kind,
            title
        }

        if (id) data.id = id
        else {
            data.org = org.id
            data.initialRoomKind = 'treatment'
        }

        /*
        if (!valid) {
            // daySchedules, no providers
            window.alert('Title is required.')
            return
        } else if (daySchedules?.length && !providers?.length) {
            // daySchedules, no providers
            window.alert('Providers are required when specifying a service schedule.')
            return
        } else if (!daySchedules?.length && providers?.length) {
            // no daySchedules, providers
            window.alert('Service schedule is required when specifying providers.')
            return
        }
        */

        setRequesting(true)

        mutation({
            variables: {
                input: data
            }
        }).then(response => {
            const mutationName = Object.keys(response.data)
            const { errors, service } = response.data[mutationName[0]]

            if (service) {
                const isNewService = !id // Check if this was a create operation

                if (daySchedules) {
                    saveDaySchedules(daySchedules, 0, null, service.id, () => addProvidersToService(providers, 0, service.id, isNewService))
                } else {
                    // No day schedules - activate immediately if new service
                    if (isNewService) {
                        activateService(service.id)
                    } else {
                        goToServicesRoute()
                    }
                }
            } else {
                setRequesting(false)
                window.alert(errors[0].message)
            }
        }).catch(error => {
            setRequesting(false)
            window.alert(error.message)
        })
    }

    function saveDaySchedules(arr, i, providerId, serviceId, onSuccess) {
        if (arr.length === i) {
            if (onSuccess) onSuccess()
            else goToServicesRoute()
        } else {
            const { concurrency, create, day, dayStr, id, remove, startTime, endTime } = arr[i]
            const next = () => saveDaySchedules(arr, i + 1, providerId, serviceId, onSuccess)

            if (id && remove) {
                serviceDayScheduleRemove({
                    variables: {
                        input: {
                            id
                        }
                    }
                }).then(next)
            } else if (id || create) {
                const mutation = (id ? serviceDayScheduleUpdate : serviceDayScheduleCreate)
                const input = {
                    id,
                    concurrency,
                    service: serviceId,
                    startTime: parseInt(startTime.format('HHmm')),
                    endTime: parseInt(endTime.format('HHmm')),
                    onlineStartTime: parseInt(startTime.format('HHmm')),
                    onlineEndTime: parseInt(endTime.format('HHmm')),
                    dayNum: day,
                    dayStr,
                    provider: providerId
                }

                if (id) input.id = id

                mutation({ variables: { input } }).then(response => {
                    const key = Object.keys(response.data)[0]
                    const { errors, daySchedule } = response.data[key]

                    if (daySchedule) {
                        next()
                    } else {
                        window.alert(errors[0].message)
                    }
                })
            } else {
                next()
            }
        }
    }

    if (onload || loading || rosterLoading) return <LoadingPane />

    return (
        <div className='setup-service-view'>
            {requesting && <LoadingPane style={{ position: 'fixed' }} />}

            {edit &&
                <div className='floating-button-frame'>
                    <IconButton
                        onClick={cancel}
                        title={{
                            color: '#747A7A',
                            label: 'Cancel'
                        }}
                    />

                    <IconButton
                        className={valid ? '' : 'disabled'}
                        icon={require('../../../theme/assets/check-icon-white.png')}
                        iconHeight={9.17}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={save}
                        title={{
                            color: 'white',
                            label: 'Save'
                        }}
                    />
                </div>
            }

            <div className='sections-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Service Details</div>

                    <div className='form-frame'>
                        <FloatingTextField
                            label='Service title'
                            onChange={text => handleChange('title', text)}
                            value={service?.title}
                        />

                        <SelectMenu
                            label='Service type'
                            options={serviceKinds}
                            onChange={value => handleChange('kind', value)}
                            placeholder='Select service type'
                            value={service?.kind}
                        />

                        <SelectMenu
                            label='Service length (min)'
                            options={generateOptions(1, 15, 15)}
                            onChange={value => handleChange('durationMins', value)}
                            placeholder='Select service length'
                            value={service?.durationMins}
                        />

                        <textarea
                            className='service-textarea'
                            onChange={e => handleChange('desc', e.target.value)}
                            placeholder='Enter service description'
                            value={service?.desc ?? ''}
                        />
                    </div>
                </div>

                <div className='section-frame'>
                    <div className='section-header'>Service Schedule</div>

                    <DaySchedules
                        daySchedules={service?.daySchedules}
                        onChange={daySchedules => handleChange('daySchedules', daySchedules)}
                    />
                </div>

                <div className='section-frame'>
                    <div className='section-header-frame'>
                        <div className='section-header'>Providers</div>

                        {(providers.length > 0) && <button className='add-button' onClick={() => setModal(true)}>Select Provider(s)</button>}
                    </div>

                    {providers?.length ?
                        <TableView
                            headers={[
                                { title: 'Name', width: '30%' },
                                { title: 'Gender', width: '20%' },
                                { title: 'Email', width: '30%' },
                                { title: 'Phone', width: '20%' },
                            ]}
                            data={providers.map(provider => {
                                const user = formatUser(rosterData?.org.users.nodes.find(u => provider === u.id))

                                return {
                                    ...user,
                                    nameFrame: (
                                        <div className='name-frame'>
                                            <Avatar
                                                width={32}
                                                user={user}
                                            />

                                            <div className='cell-label full-name ellipsis'>{user.fullName}</div>
                                        </div>
                                    )
                                }
                            })}
                            keys={['nameFrame', 'gender', 'email', 'phone']}
                        />
                        :
                        <div className='hint-frame'>
                            <div className='hint-label'>Add providers on your staff roster who can perform this service.</div>
                            <button className='hint-button' onClick={() => setModal(true)}>Select Provider(s)</button>
                        </div>
                    }
                </div>
            </div>

            {modal &&
                <ProvidersPane
                    onHide={() => setModal(false)}
                    handleChange={handleChange}
                    org={rosterData?.org}
                    service={service}
                />
            }
        </div>
    )
}

export default SetupService