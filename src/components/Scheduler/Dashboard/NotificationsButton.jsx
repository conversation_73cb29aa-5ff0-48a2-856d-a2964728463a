import { useState } from 'react'
import { useQuery } from '@apollo/client'

import GetUserNotificationsQuery from '../../../graphql/queries/components/Scheduler/Dashboard/Notifications'
import NotificationsPanel from './NotificationsPanel'

const NotificationsButton = () => {
    const [showPanel, setShowPanel] = useState(false)

    // Temporary query - will be updated when we have the correct schema
    useQuery(GetUserNotificationsQuery, {
        pollInterval: 30000, // Poll every 30 seconds for new notifications
    })

    // Mock data for now - replace with real data when schema is available
    const unreadCount = 0 // Mock unread count

    const handleTogglePanel = () => {
        setShowPanel(!showPanel)
    }

    const handleClosePanel = () => {
        setShowPanel(false)
    }

    return (
        <div className="notifications-container">
            <button 
                className="notifications-button"
                onClick={handleTogglePanel}
                title="Notifications"
            >
                <img
                            className='bell-icon'
                            alt='bell-icon'
                            src={require('../../../theme/assets/bell-icon.png')}
                            style={{ width: "35px", height: "35px" }}
                        />
                {unreadCount > 0 && (
                    <span className="notification-badge">
                        {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                )}
            </button>

            {showPanel && (
                <div className="notifications-overlay">
                    <div className="notifications-backdrop" onClick={handleClosePanel} />
                    <NotificationsPanel onClose={handleClosePanel} />
                </div>
            )}
        </div>
    )
}

export default NotificationsButton
