import PropTypes from 'prop-types'
import Checkbox from './Checkbox'

const CheckboxGroup = ({ label, options, value = [], onChange, placeholder }) => {
    const handleOptionChange = (optionId, checked) => {
        let newValue
        if (checked) {
            // Add option if not already selected
            newValue = value.includes(optionId) ? value : [...value, optionId]
        } else {
            // Remove option if selected
            newValue = value.filter(id => id !== optionId)
        }
        onChange(newValue)
    }

    return (
        <div className='checkbox-group-view'>
            {label && <div className='checkbox-group-label'>{label}</div>}
            {placeholder && value.length === 0 && (
                <div className='checkbox-group-placeholder'>{placeholder}</div>
            )}
            <div className='checkbox-options'>
                {options.map(option => (
                    <div key={option.id} className='checkbox-option'>
                        <Checkbox
                            title={option.value}
                            value={value.includes(option.id)}
                            onChange={(checked) => handleOptionChange(option.id, checked)}
                        />
                    </div>
                ))}
            </div>
        </div>
    )
}

CheckboxGroup.propTypes = {
    label: PropTypes.string,
    options: PropTypes.arrayOf(PropTypes.shape({
        id: PropTypes.string.isRequired,
        value: PropTypes.string.isRequired
    })).isRequired,
    value: PropTypes.arrayOf(PropTypes.string),
    onChange: PropTypes.func.isRequired,
    placeholder: PropTypes.string
}

export default CheckboxGroup
