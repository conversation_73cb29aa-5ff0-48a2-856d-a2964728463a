import { gql } from '@apollo/client'

const MarkNotificationReadMutation = gql`
    mutation MarkNotificationRead($id: ID!) {
        notificationMarkRead(input: { id: $id }) {
            success
            result {
                id
                subject
                content
                readAtIso
                readAt
            }
            errors {
                message
            }
        }
    }
`

export default MarkNotificationReadMutation
