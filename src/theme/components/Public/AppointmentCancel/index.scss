.appointment-cancel-view {
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding: 20px;
    
    .cancel-container, .error-container, .success-container {
        background: white;
        border-radius: 16px;
        padding: 48px;
        max-width: 650px;
        width: 100%;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        text-align: center;
        
        .wellup-logo {
            width: 120px;
            height: auto;
            margin-bottom: 32px;
        }
        
        h2 {
            color: #008390;
            margin-bottom: 24px;
            font-size: 28px;
            font-weight: 600;
            line-height: 1.3;
        }
        
        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 16px;
            color: #495057;
        }
        
        .location-info {
            background: #f8f9fa;
            padding: 24px;
            border-radius: 12px;
            margin: 32px 0;
            border-left: 4px solid #008390;
            text-align: left;
            
            strong {
                font-size: 20px;
                color: #008390;
                font-weight: 600;
                display: block;
                margin-bottom: 8px;
            }
            
            br + text {
                color: #6c757d;
                font-size: 15px;
                line-height: 1.5;
            }
        }
        
        .appointment-details {
            text-align: left;
            margin: 32px 0;
            background: #fff8e1;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            
            p {
                margin-bottom: 12px;
                font-size: 15px;
                
                &:last-child {
                    margin-bottom: 0;
                }
                
                strong {
                    color: #495057;
                    font-weight: 600;
                }
            }
        }
        
        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 40px;
            flex-wrap: wrap;
            
            .icon-button {
                min-width: 200px;
                height: 48px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 15px;
                transition: all 0.2s ease;
                
                &:hover:not(:disabled) {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                }
                
                &:disabled {
                    opacity: 0.7;
                    cursor: not-allowed;
                }
            }
        }
        
        .home-button {
            background: linear-gradient(180deg, #008390 0%, #006B75 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            
            &:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0, 131, 144, 0.3);
            }
        }
    }
    
    .error-container {
        border-left: 4px solid #E42B57;
        
        h2 {
            color: #E42B57;
        }
        
        p {
            color: #721c24;
        }
    }
    
    .success-container {
        border-left: 4px solid #0CA44C;
        
        h2 {
            color: #0CA44C;
        }
        
        p {
            color: #155724;
        }
    }
    
    // Responsive design
    @media (max-width: 768px) {
        padding: 15px;
        
        .cancel-container, .error-container, .success-container {
            padding: 32px 24px;
            
            h2 {
                font-size: 24px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
                
                .icon-button {
                    width: 100%;
                    max-width: 280px;
                }
            }
        }
    }
}
