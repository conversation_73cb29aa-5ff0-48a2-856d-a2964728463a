import { useEffect, useMemo, useState } from "react"
import { OverlayTrigger } from "react-bootstrap"
import { useNavigate, useSearchParams } from "react-router"
import { ToastContainer, toast, Slide } from "react-toastify"
import { useLazyQuery } from "@apollo/client"

import _ from 'lodash'
import moment from "moment"

import AppointmentsQuery from "../../../graphql/queries/components/Scheduler/Appointments"

import { generateDaysOfWeek } from "../../../helpers/Functions"

import useApptStatus from "../../../hooks/useApptStatus"
import useCurrentRole from "../../../hooks/useCurrentRole"
import useConstants from "../../../hooks/useConstants"

import AppointmentStatus from '../Appointment/Status'

import ServicesPane from './panes/Services'

import Avatar from "../../Shared/Avatar"
import DateRangePicker from "../../Shared/DateRangePicker"
import IconButton from '../../Shared/IconButton'
import LoadingPane from '../../Shared/LoadingPane'
import SegmentedButton from '../../Shared/SegmentedButton'
import SelectMenu from '../../Shared/SelectMenu'
import TableView from '../../Shared/TableView'

const SchedulerAppointments = () => {
    const { currentRole } = useCurrentRole()
    const { apptKinds, apptStatuses } = useConstants()
    const {
        apptCancel, apptCheckIn, apptComplete, apptLeft, apptNoShow, apptStart, apptUndo,
        queueCancel, queueCheckIn, queueIgnore, queueLeft, queueNoShow, queueStart, queueUndo
    } = useApptStatus()

    const navigate = useNavigate()
    const [searchParams] = useSearchParams()

    const [org, setOrg] = useState()
    const [appointments, setAppointments] = useState([])
    const [dateButtons, setDateButtons] = useState(generateDaysOfWeek())
    const [add, setAdd] = useState(false)

    const [fetchAppointments, { loading }] = useLazyQuery(AppointmentsQuery)

    // statuses used for calculating completion
    const completedStatuses = ['cancelled', 'completed', 'ignored', 'left', 'no_show']

    useEffect(() => {
        const notification = searchParams.get('notify')

        if (notification) handleNotification(parseInt(notification))
    }, [])

    // date was selected - fetch appointments
    useEffect(() => {
        const selected = dateButtons.find(o => o.selected)

        if (selected?.date && currentRole?.org?.id) {
            fetchAppointments({
                variables: {
                    id: currentRole?.org?.id,
                    date: selected.date,
                    status: ['arrived', 'cancelled', 'complete', 'ignored', 'left', 'no_show', 'pending', 'ready', 'roomed']
                }
            }).then(response => {
                const { org } = response.data

                if (org) {
                    if (org.services.length === 0 && org.providerCount === 0) {
                        navigate('/dashboard/setup/details')
                    } else {
                        const arr = org.services.map(s => [s.appointments.nodes, s.queue.nodes].flat()).flat()

                        handleAppointmentsChange(arr)
                    }

                    setOrg(org)
                }
            }).catch(error => {
                window.alert(error.message)
            })
        }
    }, [dateButtons])

    function goToApptRoute(appt) {
        const type = appt.__typename

        if (type === 'ServiceQueueEntry') {
            navigate(`/dashboard/services/${appt.service.id}/queue/${appt.id}`)
        } else {
            navigate(`/dashboard/appointments/${appt.id}`)
        }

    }

    function handleAppointmentsChange(arr) {
        const arrWithRank = arr.map(o => ({ ...o, rank: completedStatuses.includes(o.status) ? 2 : 1 }))

        // setAppointments(arr)
        setAppointments(_.orderBy(arrWithRank, ['rank', 'beginAtIso']))
    }

    function handleDateSelected(d) {
        const buttons = generateDaysOfWeek(d)
        const formattedDateButtons = buttons.map(b => {
            return {
                ...b,
                selected: b.date.isSame(d)
            }
        })

        setDateButtons(formattedDateButtons)
    }

    function handleSegmentedButton(button) {
        setDateButtons(dateButtons.map(o => ({ ...o, selected: o.date === button.date })))
    }

    function handleNotification(notificationId) {
        const options = {
            position: 'bottom-center',
            autoClose: 2500,
            hideProgressBar: true,
            transition: Slide
        }

        /*  
            notification index mappings:
            1: appointment creation
            2: walk in creation
        */

        toast(`${notificationId === 1 ? 'Appointment' : 'Walk-in'} booked successfully`, options)

        navigate('/dashboard/appointments')
    }

    function goToRoute(route) {
        setAdd(false)

        navigate(`/dashboard/appointments/${route}`)
    }

    function updateApptStatus(appt, status) {
        const isQueue = (appt.__typename === "ServiceQueueEntry")
        const mutations = isQueue ?
            {
                'cancel!': queueCancel,
                'check_in!': queueCheckIn,
                'left!': queueLeft,
                'ignore!': queueIgnore,
                'no_show!': queueNoShow,
                'start!': queueStart,
                'undo!': queueUndo
            }
            : {
                'cancel!': apptCancel,
                'check_in!': apptCheckIn,
                'complete!': apptComplete,
                'ignore!': apptLeft,
                'no_show!': apptNoShow,
                'start!': apptStart,
                'undo!': apptUndo

            }

        const mutation = mutations[status]

        if (mutation) {
            mutation({
                variables: {
                    input: {
                        id: appt.id
                    }
                }
            }).then(response => {
                const key = Object.keys(response.data)[0]
                const { errors, appointment, queueEntry } = response.data[key]
                const result = (appt.__typename === 'ServiceQueueEntry') ? queueEntry : appointment

                if (result) {
                    const arr = appointments
                    const i = appointments.findIndex(a => a.id === result.id)

                    handleAppointmentsChange([...arr.slice(0, i), result, ...arr.slice(i + 1, arr.length)])
                } else {
                    window.alert(errors[0].message)
                }
            }).catch(error => {
                window.alert(error.message)
            })
        }
    }

    const hasApptService = useMemo(() => org?.services.find(o => o.kind === 'appointment' && o.status === 'active'), [org])
    const hasQueueService = useMemo(() => org?.services.find(o => o.kind === 'queue' && o.status === 'active'), [org])
    const arr = appointments

    return (
        <div className='appointments-view full-screen-view'>
            {loading && <LoadingPane />}

            <div className='toolbar'>
                <div className='toolbar-header'>{`Appointments (${appointments.length})`}</div>

                <div className='right-section'>
                    <DateRangePicker
                        onChange={handleDateSelected}
                        value={dateButtons.find(o => o.selected)?.date.toISOString()}
                    />

                    <OverlayTrigger
                        rootClose={true}
                        placement='right'
                        overlay={(props) => (
                            <div className='overlay-trigger-view full-screen-view appointments-pane'>
                                <div className='background full-screen-view' onClick={() => setAdd(false)} />

                                <div className='overlay-content-frame'>
                                    {hasQueueService && <button className='trigger-button' onClick={() => goToRoute('queue/create')}>Walk In</button>}
                                    {hasApptService && <button className='trigger-button teal' onClick={() => goToRoute('create')}>Scheduled Appointment</button>}
                                </div>
                            </div>
                        )}
                        show={add}
                    >
                        <IconButton
                            icon={require('../../../theme/assets/plus-icon-white.png')}
                            iconHeight={12}
                            background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                            onClick={() => setAdd(!add)}
                            title={{
                                color: 'white',
                                label: 'Add'
                            }}
                        />
                    </OverlayTrigger>
                </div>


            </div>

            <div className='segmented-button-frame'>
                <SegmentedButton
                    buttons={dateButtons}
                    onClick={button => handleSegmentedButton(button)}
                />
            </div>

            <TableView
                data={arr.map(appt => {
                    const { availableActions, beginAtIso, kind, person, provider, service, status } = appt
                    const personName = `${person?.firstName || 'Unknown'} ${person?.lastName || 'Person'}`
                    const email = person?.emails?.find(o => o.label === 'main')
                    const phone = person?.phones?.find(o => o.label === 'main')
                    const providerName = provider ? `${provider.firstName} ${provider.lastName}` : '-'
                    const selectedDate = dateButtons.find(b => b.selected) // from UI
                    const isToday = selectedDate ? selectedDate.date.startOf('day').isSame(moment().startOf('day')) : false
                    const isPastDate = (moment(beginAtIso).startOf('day').isBefore(moment().startOf('day')))

                    return {
                        ...appt,
                        className: `${(completedStatuses.includes(status)) ? ' completed' : ''}${isPastDate && !isToday ? ' no-change' : ''}`,
                        startTime: moment(beginAtIso).format('h:mmA'),
                        title: service?.title || 'Unknown Service',
                        typeFrame: {
                            sort: 'kind',
                            component: <div className='cell-label' style={{ color: apptKinds?.[kind]?.color || '#666' }}>{apptKinds?.[kind]?.title || 'Unknown Type'}</div>
                        },
                        personName,
                        personFrame: {
                            sort: 'personName',
                            component: <>
                                <div className='name-frame'>
                                    <Avatar
                                        width={32}
                                        user={person}
                                    />

                                    <div className='cell-label full-name ellipsis'>{personName}</div>

                                    <div className='profile-details-pane'>
                                        <div className='name-frame'>
                                            <div className='left-section'>
                                                <Avatar
                                                    width={22}
                                                    user={person}
                                                />

                                                <div className='cell-label full-name ellipsis'>{personName}</div>
                                            </div>

                                            <button
                                                className='view-profile-button'
                                                onClick={() => navigate(`/dashboard/setup/roster/${person?.id}`)}
                                            >View Profile</button>
                                        </div>

                                        <div className='cell-frame'>
                                            <div className='cell-label'>{`DOB: ${person?.dob ? moment(person.dob).format('MM/DD/YYYY') : 'Not Available'}`}</div>
                                        </div>

                                        {email &&
                                            <div className='cell-frame' style={{ borderBottomWidth: email && !phone ? 0 : 1 }}>
                                                <div className='cell-label'>{`Email: ${email?.address || 'Not Available'}`}</div>
                                            </div>
                                        }

                                        {phone &&
                                            <div className='cell-frame' style={{ borderBottomWidth: 0 }}>
                                                <div className='cell-label'>{`Phone: ${phone?.number || 'Not Available'}`}</div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            </>
                        },
                        providerName,
                        providerFrame: {
                            sort: 'providerName',
                            component: provider ?
                                <div className='name-frame'>
                                    <Avatar
                                        width={32}
                                        user={appt.provider}
                                    />

                                    <div className='cell-label full-name ellipsis'>{providerName}</div>
                                </div>
                                :
                                <div className='cell-label'>-</div>
                        },
                        statusFrame: {
                            sort: 'status',
                            component: <>
                                {<AppointmentStatus appointment={appt} />}

                                <div className='flex-frame'>
                                    <SelectMenu
                                        options={availableActions?.filter(action => apptStatuses[action.split('!')[0]]).map(action => ({ id: action, value: apptStatuses[action.split('!')[0]]?.title || 'Unknown Action' })) || []}
                                        onChange={value => updateApptStatus(appt, value)}
                                        placeholder={apptStatuses[status]?.title || 'Unknown Status'}
                                    />

                                    {status !== 'booked' && <button className='undo-button' onClick={() => updateApptStatus(appt, 'undo!')}>undo</button>}
                                </div>
                            </>
                        },
                        moreFrame: (
                            <div className='more-icon' onClick={() => goToApptRoute(appt)} >
                                <img
                                    alt='arrow-icon'
                                    className='arrow-icon'
                                    src={require('../../../theme/assets/arrow-icon-white-v2.png')}
                                />
                            </div>
                        )
                    }
                })}
                headers={
                    [
                        { title: 'Time', width: '10%' },
                        { title: 'Appointment', width: '20%' },
                        { title: 'Type', width: '10%' },
                        { title: 'Member', width: '20%' },
                        { title: 'Staff', width: '20%' },
                        { title: 'Status', width: '20%' },
                        { title: '', width: '40px' },
                    ]}
                keys={['startTime', 'title', 'typeFrame', 'personFrame', 'providerFrame', 'statusFrame', 'moreFrame']}
            />

            <ServicesPane
                org={org}
            />

            <ToastContainer
                icon={({ type }) => {
                    switch (type) {
                        default:
                            return <img
                                alt='check-icon'
                                className='check-icon'
                                src={require('../../../theme/assets/check-icon-teal.png')}
                            />
                    }
                }}
            />
        </div >
    )
}

export default SchedulerAppointments