@import '../../globals/Colors.scss';

.date-text-field-view {
    position: relative;
    height: 48px;
    margin-bottom: 12px;

    .text-field-frame {
        display: flex;
        width: 100%;
        height: 100%;
        overflow: hidden;
        padding: 0px 16px;
        border-radius: 6px;
        align-items: center;
        box-sizing: border-box;
        border: 1px solid $border-grey;
        justify-content: space-between;

        .text-field-value-label {
            flex-direction: column;

            .text-field-header {
                color: $grey;
                font-size: 13px;
                font-weight: 400;
                line-height: 100%;
                margin-bottom: 3px;
            }

            .text-field-value {
                font-size: 15px;
                font-weight: 500;
                line-height: 100%;
            }
        }

        .calendar-icon {
            height: 24px;
            padding-left: 5px;
        }
    }

    .react-datepicker-wrapper {
        position: absolute;
        top: 0px;
        left: 0px;
        right: 0px;
        bottom: 0px;
        // border-radius: 6px;
        // z-index: 10;

        .react-datepicker__input-container {
            width: 100%;
            height: 100%;

            input {
                width: 100%;
                height: 100%;
                cursor: pointer;
                border: none;
                margin: 0px;
                padding: 0px;
                width: 100%;
                height: 100%;
                opacity: 0;
            }
        }
    }

    .react-datepicker__tab-loop {
        position: absolute;
        z-index: 10;
    }
}