import { useEffect, useState } from "react"
import { useLazyQuery } from "@apollo/client"
import { toast, ToastContainer } from "react-toastify"

import moment from "moment"

import SchedulerRosterUserQuery from "../../../graphql/queries/components/Scheduler/Roster/User"
import UserFindOrCreateMutation from "../../../graphql/mutations/User/FindOrCreate"
import UserUpdateMutation from "../../../graphql/mutations/User/Update"
import createKioskClient from "../../../graphql/KioskApolloClient"
import { sendStaffInviteEmail } from "../../../services/NotificationService"

import useConstants from "../../../hooks/useConstants"
import useKioskAuth from "../../../hooks/useKioskAuth"

import DaySchedules from "../../Shared/DaySchedules"
import DateTextField from "../../Shared/DateTextField"
import FloatingTextField from "../../Shared/FloatingTextField"
import IconButton from "../../Shared/IconButton"
import ImageUpload from "../../Shared/ImageUpload"
import LoadingPane from "../../Shared/LoadingPane"
import SelectMenu from "../../Shared/SelectMenu"

const SetupRosterUser = (props) => {
    const { edit, navigate, org, params, setEdit } = props

    // hooks
    const constants = useConstants()
    const { authenticateKiosk } = useKioskAuth()

    const [requesting, setRequesting] = useState(false)
    const [user, setUser] = useState()
    const [step, setStep] = useState(0)
    const [steps] = useState([Step1, Step2])

    const [getUser, { loading }] = useLazyQuery(SchedulerRosterUserQuery)

    const valid = (step === 0) ? user?.type : (user?.firstName && user?.lastName && user?.email)

    useEffect(() => {
        setEdit(true)

        if (params.id !== 'add') {
            // user exists - fetch data
            getUser({
                variables: {
                    id: params.id
                }
            }).then(response => {
                setStep(1)
                setupUser(response.data.user)
            }).catch(error => {
                window.alert(error.message)
            })
        }
    }, [])

    function setupUser(user) {
        const data = { ...user }
        const email = user?.emails?.find(o => o.label === 'main')
        const phone = user?.phones?.find(o => o.label === 'main')
        const gender = user?.latestData?.find(o => o.key === 'person.gender')
        const address = user?.addresses?.find(o => o.label === 'main')
        const orgRole = user?.roles.find(o => o.org.id === org.id)

        if (email) data.email = email.address
        if (phone) data.phone = phone.number
        if (gender) data.gender = gender.values[0]

        // Load address fields if available
        if (address) {
            // Parse combined street field back into street and street2
            const streetParts = (address.street || '').split(', ')
            data.street = streetParts[0] || ''
            data.street2 = streetParts.length > 1 ? streetParts.slice(1).join(', ') : ''
            data.city = address.city || ''
            data.state = address.state || ''
            data.zip = address.zip || ''
        }

        // Load optional fields from user object
        if (user?.mainLanguage) data.mainLanguage = user.mainLanguage
        if (user?.disabilities) data.disabilities = user.disabilities
        if (orgRole) {
            data.type = orgRole.role

            if (orgRole.role === 'provider') {
                const daySchedules = user?.preferences.find(o => o.key === 'settings.day_schedules')

                if (daySchedules) {
                    const arr = JSON.parse(daySchedules.value[0])

                    data.daySchedules = arr.map(o => ({
                        ...o,
                        startTime: moment(o.startTime),
                        endTime: moment(o.endTime),
                    }))
                }
            }
        }

        setUser(data)
    }

    function cancel() {
        setEdit(false)
        navigate('/dashboard/setup/roster')
    }

    function handleChange(key, value) {
        setUser({
            ...user,
            [key]: value
        })
    }

    function handleStep(forward) {
        const nextStep = (forward && valid) ? (step + 1) : (step - 1)

        setStep(nextStep)
    }

    async function sendEmailInvite(user, email, role, organizationName) {
        try {
            await sendStaffInviteEmail({
                email: email,
                firstName: user.firstName,
                lastName: user.lastName,
                organizationName: organizationName,
                role: role
            })

            toast.success(`Invite email sent to ${email}`, {
                position: 'bottom-center',
                autoClose: 3000,
                hideProgressBar: true
            })
        } catch (error) {
            console.error('Failed to send invite email:', error)
            toast.error(`Failed to send invite email: ${error.message}`, {
                position: 'bottom-center',
                autoClose: 5000,
                hideProgressBar: true
            })
        }
    }

    function save() {
        setRequesting(true)
console.log(user?.id);
        if (user?.id) {
            updateUser()
        } else {
            findOrCreateUser()
        }
    }

    async function findOrCreateUser() {
        const { dob, firstName, lastName, type } = user

        try {
            // Authenticate with kiosk user for admin permissions
            const token = await authenticateKiosk()

            // Create kiosk client with the token for mutations
            const kioskClient = createKioskClient(token)

            const input = {
                firstName,
                lastName,
                org: org.id,
                role: type || 'staff'
            }

            // Add optional fields if provided
            if (dob) {
                input.dob = dob
            }

            const response = await kioskClient.mutate({
                mutation: UserFindOrCreateMutation,
                variables: { input }
            })

            const { errors, user } = response.data.userFindOrCreate

            if (user) {
                updateUser(user)
            } else {
                window.alert(errors[0].message)
                setRequesting(false)
            }
        } catch (error) {
            setRequesting(false)
            window.alert(error.message)
        }
    }

    async function updateUser(newUser) {
        const {
            id, daySchedules, dob, file, firstName, lastName, email, phone, gender, type, roles,
            mainLanguage, disabilities, street, street2, city, state, zip
        } = user

        try {
            // Authenticate with kiosk user for admin permissions
            const token = await authenticateKiosk()

            // Create kiosk client with the token for mutations
            const kioskClient = createKioskClient(token)

            const data = {
                id: newUser ? newUser.id : id,
                dob,
                firstName,
                lastName,
                gender
            }

            if (email) data.emails = [{ address: email }]
            if (phone) data.phones = [{ number: phone }]

            // Add optional fields
            if (mainLanguage) data.mainLanguage = mainLanguage
            if (disabilities) data.disabilities = disabilities

            // Add address if any address fields are provided
            if (street || street2 || city || state || zip) {
                // Combine street and street2 into a single street field
                const fullStreet = street ?
                    (street2 ? `${street}, ${street2}` : street) :
                    (street2 || "")

                data.addresses = [{
                    label: "main",
                    city: city || "",
                    state: state || "",
                    street: fullStreet,
                    zip: zip || ""
                }]
            }

            // setup roles
            if (type) {
                const newRole = { role: type, orgId: org.id }
                // new user, retain existing roles, otherwise override existing role with new selection
                const existingRoles = newUser ? newUser.roles : roles.filter(r => r.org.id !== org.id)

                data.roles = [...existingRoles.map(r => ({ role: r.role, orgId: r.org.id })), newRole]
            }

            if (file) data.attachments = [{ label: 'profile_picture', base64: file }]

            // setup default provider schedule
            if (daySchedules) {
                const schedules = daySchedules.filter(d => !d.remove).map(daySchedule => {
                    const { concurrency, day, dayStr, startTime, endTime } = daySchedule

                    return {
                        concurrency,
                        startTime,
                        endTime,
                        onlineStartTime: startTime,
                        onlineEndTime: endTime,
                        day,
                        dayStr
                    }
                })

                data.preferences = [{ key: 'settings.day_schedules', value: JSON.stringify(schedules) }]
            }

            const response = await kioskClient.mutate({
                mutation: UserUpdateMutation,
                variables: {
                    orgId: org.id,
                    input: data
                }
            })

            const { errors, user } = response.data.userUpdate

            setRequesting(false)

            if (user) {
                // Send email invite for new staff members
                if (newUser && email && type !== 'person') {
                    sendEmailInvite(user, email, type, org.name)
                }

                setEdit(false)
                navigate('/dashboard/setup/roster')
            } else {
                console.log(errors)
                window.alert(errors[0].message)
            }
        } catch (error) {
            setRequesting(false)
    console.error('Error updating staff member:', error)
    window.alert(error?.message || 'Failed to update staff member. Please try again.')
        }
    }

    const Component = steps[step]   // dynamic step component

    if (loading) return <LoadingPane />

    return (
        <div className='setup-roster-user-view'>
            {requesting && <LoadingPane style={{ position: 'fixed' }} />}

            {edit &&
                <div className='floating-button-frame'>
                    <IconButton
                        onClick={cancel}
                        title={{
                            color: '#747A7A',
                            label: 'Cancel'
                        }}
                    />
                </div>
            }

            <div className='step-label'>{`Step ${step + 1} of ${steps.length}`}</div>

            <Component
                constants={constants}
                handleChange={handleChange}
                handleStep={handleStep}
                save={save}
                user={user}
                valid={valid}
            />

            <ToastContainer
                position="bottom-center"
                autoClose={3000}
                hideProgressBar={true}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
            />
        </div>
    )
}

const Step1 = ({ constants, handleChange, handleStep, user, valid }) => {
    const { staffRoles } = constants

    return (
        <div className='step-content-view'>
            <div className='sections-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Select Staff Member Type</div>

                    <SelectMenu
                        className='staff-type-menu'
                        options={staffRoles}
                        onChange={value => handleChange('type', value)}
                        placeholder='Select staff member type'
                        value={user?.type}
                    />
                </div>
            </div>

            <div className='bottom-toolbar'>
                <IconButton
                    className={valid ? '' : 'disabled'}
                    iconAlign='right'
                    icon={require('../../../theme/assets/arrow-icon-white.png')}
                    iconHeight={18}
                    background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                    onClick={() => { if (valid) handleStep(true) }}
                    title={{
                        color: 'white',
                        label: 'Continue'
                    }}
                />
            </div>
        </div>
    )
}

const Step2 = ({ constants, handleChange, handleStep, save, user, valid }) => {
    const { genders, languages } = constants
    const isProvider = (user?.type === 'provider')

    return (
        <div className='step-content-view'>
            <div className='sections-frame scroll'>
                <div className='section-frame'>
                    <div className='section-header'>Member Details</div>

                    <div>
                        <ImageUpload
                            onChange={file => handleChange('file', file)}
                            user={user}
                        />

                        <div className='form-frame'>
                            <FloatingTextField
                                label='First name'
                                onChange={text => handleChange('firstName', text)}
                                value={user?.firstName}
                            />

                            <FloatingTextField
                                label='Last name'
                                onChange={text => handleChange('lastName', text)}
                                value={user?.lastName}
                            />

                            <DateTextField
                                label='Date of Birth'
                                placeholder='Select date of birth'
                                onChange={d => handleChange('dob', moment(d).format('YYYY-MM-DD'))}
                                value={user?.dob}
                            />

                            <FloatingTextField
                                label='Email address'
                                onChange={text => handleChange('email', text)}
                                value={user?.email}
                            />

                            <FloatingTextField
                                label='Phone'
                                onChange={text => handleChange('phone', text)}
                                type='tel'
                                value={user?.phone}
                            />

                            <SelectMenu
                                label='Gender'
                                options={genders}
                                onChange={value => handleChange('gender', value)}
                                placeholder='Select Gender'
                                value={user?.gender}
                            />

                            <SelectMenu
                                label='Main Language (Optional)'
                                options={languages}
                                onChange={value => handleChange('mainLanguage', value)}
                                placeholder='Select primary language'
                                value={user?.mainLanguage}
                            />
                        </div>
                    </div>
                </div>

                {isProvider &&
                    <>
                        <div className='section-frame'>
                            <div className='section-header'>Provider Schedule</div>

                            <div className='hint-frame'>
                                <div className='hint-label'>Add the days and time windows this provider works.</div>
                            </div>

                            <DaySchedules
                                daySchedules={user?.daySchedules}
                                onChange={daySchedules => handleChange('daySchedules', daySchedules)}
                            />
                        </div>

                        {/* <div className='section-frame'>
                            <div className='section-header'>Vacation / Out of Office Days</div>

                            <div className='hint-frame'>
                                <div className='hint-label'>Add the dates this provider will not be available for appointments.</div>
                                <button className='hint-button'>+ Add Date Range</button>
                            </div>
                        </div> */}
                    </>
                }
            </div>

            <div className='bottom-toolbar' style={{ justifyContent: 'space-between' }}>
                <IconButton
                    icon={require('../../../theme/assets/arrow-icon-black.png')}
                    iconHeight={18}
                    onClick={() => handleStep(false)}
                    title={{
                        color: '#262D2D',
                        label: 'Previous'
                    }}
                />

                <IconButton
                    className={valid ? '' : 'disabled'}
                    icon={require('../../../theme/assets/check-icon-white.png')}
                    iconHeight={9.17}
                    background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                    onClick={() => { if (valid) save() }}
                    title={{
                        color: 'white',
                        label: 'Save'
                    }}
                />
            </div>
        </div>
    )
}

export default SetupRosterUser