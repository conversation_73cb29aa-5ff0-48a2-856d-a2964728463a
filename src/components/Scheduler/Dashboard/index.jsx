import { useContext, useEffect, useState } from "react"
import { useNavigate, useParams, useLocation, Outlet } from "react-router"

import { UserContext } from '../../../context/User'
import { RoleContext } from '../../../context/Role'

import DashboardNavigation from "./Navigation"
import NotificationsButton from "./NotificationsButton"

import RoleSelector from "../../Shared/RoleSelector"

// eslint-disable-next-line react-hooks/rules-of-hooks
const Dashboard = (props) => {
    const user = useContext(UserContext)
    const { role } = useContext(RoleContext)

    const [selectedRole] = useState(role)
    const navigate = useNavigate()
    const params = useParams()
    const location = useLocation()

    useEffect(() => {
        if (location.pathname === '/dashboard') navigate('/dashboard/appointments')
    }, [location.pathname, navigate])

    useEffect(() => {
        // handles any role changes or changes from another tab
        if (role?.id && selectedRole?.id && role.id !== selectedRole.id) {
            window.location.reload()
        }
    }, [role, selectedRole])

    // Don't render if user data isn't loaded yet
    if (!user) {
        return <div>Loading...</div>
    }

    // renderContentView function removed since we're using Outlet for nested routing

    return (
        <div className='dashboard-view full-screen-view'>
            <div className='toolbar'>
                {/* Platform switching removed - only scheduler supported */}
                <div className='platform-button'>
                    <img
                        alt='platform-icon'
                        className='platform-icon'
                        src={require('../../../theme/assets/platform-icon.png')}
                    />

                    <div className='platform-label'>Scheduler</div>
                </div>

                <div className='right-section'>
                    <RoleSelector user={user} />
                    <NotificationsButton />
                </div>
            </div>

            <div className='content-frame'>
                <DashboardNavigation
                    navigate={navigate}
                    location={location}
                    params={params}
                />

                <div className='document-frame scroll'>
                    <Outlet />
                </div>
            </div>
        </div>
    )
}

export default Dashboard