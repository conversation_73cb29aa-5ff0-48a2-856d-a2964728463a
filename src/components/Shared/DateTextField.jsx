import { useEffect, useState } from "react"

import PropTypes from "prop-types"
import DatePicker from "react-datepicker"

import moment from 'moment'

const DateTextField = (props) => {
    const { label, onChange, placeholder, value, minDate, maxDate, excludeDates, clearAfterSelect } = props
    const [date, setDate] = useState(value ? moment(value, 'YYYY-MM-DD').toDate() : null)

    useEffect(() => {
        setDate(value)
    }, [value])

    return (
        <div className='date-text-field-view'>
            <div className='text-field-frame'>
                {date ?
                    <div className='text-field-value-label'>
                        <div className='text-field-header'>{label}</div>
                        <div className='text-field-value'>{moment(date).format('MMM D, YYYY')}</div>
                    </div>
                    :
                    <div className='placeholder-label'>{placeholder}</div>
                }

                <img
                    alt='calendar-icon'
                    className='calendar-icon'
                    src={require('../../theme/assets/calendar-icon-orange.png')}
                />
            </div>

            <DatePicker
                selected={date}
                onChange={d => {
                    if (onChange) onChange(d)
                    // Clear the date if clearAfterSelect is true (useful for multi-select)
                    setDate(clearAfterSelect ? null : d)
                }}
                minDate={minDate}
                maxDate={maxDate}
                excludeDates={excludeDates}
                peekNextMonth
                showMonthDropdown
                showYearDropdown
                dropdownMode="select"
            />
        </div>
    )
}

DateTextField.propTypes = {
    label: PropTypes.string,
    onChange: PropTypes.func.isRequired,
    placeholder: PropTypes.string,
    value: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
    minDate: PropTypes.object,
    maxDate: PropTypes.object,
    excludeDates: PropTypes.arrayOf(PropTypes.object),
    clearAfterSelect: PropTypes.bool
}

export default DateTextField