@import '../../../globals/Colors.scss';

.networks-view {
    display: flex;
    flex-direction: column;

    .toolbar {
        display: flex;
        flex: 0 0 60px;
        padding: 0px 20px;
        align-items: center;
        box-sizing: border-box;
        justify-content: space-between;
        box-shadow: 0px 1px 0px 0px #E0E0E0;

        .toolbar-header {
            font-size: 21px;
            font-weight: 600;
        }
    }

    .content-frame {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .search-section {

        .search-bar {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px;

            .floating-text-field {
                width: 600px;
            }

            .floating-text-field-view {
                flex: 1;
                max-height: 45px;
                
                .floating-text-field {
                    background: white;
                    border: 1px solid #E0E0E0;
                    border-radius: 6px;
                    transition: border-color 0.2s ease;
                    height: 45px;
                    margin-bottom: 0px;

                    &:focus-within {
                        border-color: $teal;
                    }

                    .floating-text-field-input {
                        padding: 12px 15px;
                        font-size: 15px;
                        line-height: 1.4;
                        height: 45px;
                        box-sizing: border-box;
                    }

                    .floating-text-field-label {
                        left: 15px;
                        font-size: 15px;
                        color: #999;

                        &.active {
                            color: $teal;
                            font-size: 12px;
                        }
                    }
                }
            }

            .clear-search-button {
                flex-shrink: 0;
                width: 145px;
                height: 45px;
                border-radius: 6px;
                background: white;
                border: 1px solid #E0E0E0;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
    margin-bottom: 13px;

                &:hover {
                    border-color: #E42B57;
                    background: #fef2f2;
                }

                img {
                    width: 16px;
                    height: 16px;
                    opacity: 0.6;
                }
            }
        }

        .search-results-info {
            font-size: 14px;

            .no-results {
                color: #666;
                font-style: italic;
                padding: 6px 12px;
                background: #fff3cd;
                border: 1px solid #ffeaa7;
                border-radius: 4px;
                display: inline-block;
            }

            .results-count {
                color: $teal;
                font-weight: 500;
                padding: 6px 12px;
                background: rgba(0, 131, 144, 0.1);
                border-radius: 4px;
                display: inline-block;
                margin-left: 15px;
                margin-bottom: 15px;
            }
        }
    }

    .table-view {
        flex: 1 1;
        border: none;
        overflow: initial;
        background: white;
        border-radius: 6px;
        margin: 0 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

        .table-headers {
            border: none;
            background: #f8f9fa;
            border-radius: 6px 6px 0 0;

            .table-header {
                font-weight: 600;
                color: #495057;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                padding: 15px;
            }
        }

        .hint-label {
            margin: 40px 20px;
            text-align: center;
            color: #666;
            font-style: italic;
            font-size: 16px;
        }

        .table-data-cell {
            position: relative;
            border: none !important;
            border-bottom: 1px solid #f0f0f0 !important;
            overflow: hidden;
            padding: 15px;

            &:last-child {
                border-bottom: none !important;
                border-radius: 0 0 6px 6px;
            }

            .name-frame {
                display: flex;
                align-items: center;

                .org-name {
                    font-weight: 600;
                    font-size: 15px;
                    color: $dark-text;
                    line-height: 1.4;
                }
            }

            .address-frame, .phone-frame, .desc-frame {
                .address-item, .phone-item, .org-desc {
                    font-size: 14px;
                    color: #666;
                    line-height: 1.4;
                    margin-bottom: 4px;

                    &:last-child {
                        margin-bottom: 0;
                    }
                }
            }

        }

        .table-data-cell:hover {
            background: #f8f9fa;
            cursor: pointer;
        }
    }

    .pagination-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: #fafafa;
        border-top: 1px solid #E0E0E0;

        .pagination-info {
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }

        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 10px;

            .pagination-button {
                width: 45px;
                height: 45px;
                border-radius: 6px;
                background: white;
                border: 1px solid #E0E0E0;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover:not(.disabled) {
                    border-color: $teal;
                    background: rgba(0, 131, 144, 0.05);
                }

                &.disabled {
                    border-color: #f0f0f0;
                    background: #f8f9fa;
                    cursor: not-allowed;
                    opacity: 0.5;
                }

                img {
                    width: 14px;
                    height: 14px;
                    opacity: 0.7;
                }
            }

            .pagination-number {
                width: 45px;
                height: 45px;
                border-radius: 6px;
                border: 1px solid #E0E0E0;
                background: white;
                color: #666;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover:not(.active) {
                    border-color: $teal;
                    color: $teal;
                    background: rgba(0, 131, 144, 0.05);
                }

                &.active {
                    background: $teal;
                    border-color: $teal;
                    color: white;
                }
            }
        }
    }
}
