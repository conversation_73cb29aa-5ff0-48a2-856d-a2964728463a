import { gql } from "@apollo/client"
import { USER_FRAGMENT } from '../../../../fragments/User'

const AppointmentCreateUsersQuery = gql`
    query AppointmentCreateUsersQuery($id: ID) {
        org(id: $id) {
            id
            users(role: "person") {
                nodes {
                    ...UserFragment
                }
            }
        }
    }
    ${USER_FRAGMENT}
`

export default AppointmentCreateUsersQuery
