@import '../../globals/Colors.scss';

.members-view {
    display: flex;
    flex-direction: column;
    background: #f8f9fa;

    .toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px;
        background: white;
        border-bottom: 1px solid #e5e7eb;

        .toolbar-header {
            font-size: 24px;
            font-weight: 600;
            color: #111827;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }
    }

    .add-member-form {
        background: white;
        border-bottom: 1px solid #e5e7eb;
        padding: 24px;
        margin: 0;

        .form-header {
            font-size: 18px;
            font-weight: 600;
            color: #111827;
            margin-bottom: 16px;
        }

        .form-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-bottom: 16px;

            input {
                padding: 12px;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                font-size: 14px;
                background: white;

                &:focus {
                    outline: none;
                    border-color: #fd8205;
                    box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
                }

                &::placeholder {
                    color: #9ca3af;
                }
            }

            .ethnicity-field {
                grid-column: 1 / -1;
                margin-top: 8px;
            }
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;

            button {
                padding: 10px 20px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                border: 1px solid #d1d5db;
                background: white;
                color: #374151;

                &:hover {
                    background: #f9fafb;
                }

                &.primary {
                    background: linear-gradient(180deg, #FD8205 0%, #E97100 100%);
                    color: white;
                    border-color: #fd8205;

                    &:hover {
                        background: linear-gradient(180deg, #E97100 0%, #D96600 100%);
                    }
                }
            }
        }
    }

    .content-frame {
        flex: 1;
        padding: 0;
        overflow: auto;

        .hint-frame {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            text-align: center;
            margin: 24px;
            width: 100%;

            .hint-label {
                color: #6b7280;
                font-size: 16px;
                margin-bottom: 16px;
            }

            .hint-button {
                padding: 12px 24px;
                background: linear-gradient(180deg, #FD8205 0%, #E97100 100%);
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;

                &:hover {
                    background: linear-gradient(180deg, #E97100 0%, #D96600 100%);
                }
            }
        }

        .fullscreen-table {
            height: 100%;
            width: 100%;

            .table-view {
                height: 100%;
                border-radius: 0;
                border: none;
                box-shadow: none;
            }
        }
    }

    .name-frame {
        display: flex;
        align-items: center;
        gap: 12px;

        .cell-label {
            font-weight: 500;
        }
    }

    .actions-frame {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        .edit-button {
            padding: 6px 12px;
            background: linear-gradient(180deg, #008390 0%, #006B75 100%);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: linear-gradient(180deg, #006B75 0%, #005A63 100%);
                transform: translateY(-1px);
            }
        }

        .delete-button {
            padding: 6px 12px;
            background: linear-gradient(180deg, #E42B57 0%, #C41E3A 100%);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
                background: linear-gradient(180deg, #C41E3A 0%, #A01729 100%);
                transform: translateY(-1px);
            }
        }

        .kiosk-user-label {
            padding: 6px 12px;
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            text-align: center;
            cursor: help;
        }
    }

    .edit-member-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;

        .modal-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            position: relative;
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

            .modal-header {
                font-size: 18px;
                font-weight: 600;
                color: #111827;
                margin-bottom: 16px;
            }

            .modal-body {
                color: #374151;
                line-height: 1.5;
                margin-bottom: 24px;

                .edit-form-sections {
                    display: flex;
                    flex-direction: column;
                    gap: 24px;

                    .form-section {
                        .section-header {
                            font-size: 16px;
                            font-weight: 600;
                            color: #111827;
                            margin-bottom: 16px;
                            padding-bottom: 8px;
                            border-bottom: 1px solid #e5e7eb;
                        }

                        .form-fields {
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 16px;

                            .floating-text-field-view,
                            .select-menu-view {
                                grid-column: span 1;
                            }

                            // Make some fields span full width
                            .floating-text-field-view:has(textarea),
                            .ethnicity-field,
                            input[type="date"] {
                                grid-column: 1 / -1;
                            }

                            input[type="date"] {
                                padding: 12px;
                                border: 1px solid #d1d5db;
                                border-radius: 6px;
                                font-size: 14px;
                                background: white;

                                &:focus {
                                    outline: none;
                                    border-color: #fd8205;
                                    box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
                                }
                            }
                        }
                    }
                }

                // Legacy support for old form structure
                .edit-form-fields {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 16px;

                    input {
                        padding: 12px;
                        border: 1px solid #d1d5db;
                        border-radius: 6px;
                        font-size: 14px;
                        background: white;

                        &:focus {
                            outline: none;
                            border-color: #fd8205;
                            box-shadow: 0 0 0 3px rgba(253, 130, 5, 0.1);
                        }

                        &::placeholder {
                            color: #9ca3af;
                        }
                    }

                    input[type="date"] {
                        grid-column: 1 / -1;
                    }
                }
            }

            .modal-actions {
                display: flex;
                gap: 12px;
                justify-content: flex-end;

                button {
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    border: 1px solid #d1d5db;
                    background: white;
                    color: #374151;

                    &:hover {
                        background: #f9fafb;
                    }

                    &.primary {
                        background: linear-gradient(180deg, #FD8205 0%, #E97100 100%);
                        color: white;
                        border-color: #fd8205;

                        &:hover {
                            background: linear-gradient(180deg, #E97100 0%, #D96600 100%);
                        }
                    }
                }
            }
        }
    }

    // SMS functionality styles
    .phone-cell {
        display: flex;
        align-items: center;
        gap: 8px;

        .sms-button {
            background: #ec811c;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.2s;
            color: white;

            &:hover {
                background: #E97100;
            }

            &:active {
                background: #E97100;
            }
        }
    }

    .sms-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;

        .modal-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
        }

        .modal-content {
            position: relative;
            background: white;
            border-radius: 8px;
            padding: 24px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);

            .modal-header {
                font-size: 18px;
                font-weight: 600;
                color: #111827;
                margin-bottom: 16px;
            }

            .modal-body {
                color: #374151;
                line-height: 1.5;
                margin-bottom: 24px;

                .sms-form {
                    .recipient-info {
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        margin-bottom: 16px;
                        padding: 12px;
                        background: #F3F4F6;
                        border-radius: 6px;

                        .recipient-label {
                            font-weight: 500;
                            color: #374151;
                        }

                        .recipient-phone {
                            font-family: monospace;
                            color: #059669;
                            font-weight: 500;
                        }
                    }

                    .message-section {
                        .message-label {
                            display: block;
                            font-weight: 500;
                            color: #374151;
                            margin-bottom: 8px;
                        }

                        .message-textarea {
                            width: 100%;
                            border: 1px solid #D1D5DB;
                            border-radius: 6px;
                            padding: 12px;
                            font-size: 14px;
                            line-height: 1.5;
                            resize: vertical;
                            min-height: 120px;
                            font-family: inherit;

                            &:focus {
                                outline: none;
                                border-color: #10B981;
                                box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
                            }
                        }

                        .character-counter {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-top: 8px;
                            font-size: 12px;
                            color: #6B7280;

                            .char-count {
                                font-weight: 500;
                            }

                            .segment-count {
                                color: #059669;
                                font-weight: 500;
                            }
                        }

                        .warning-message {
                            margin-top: 8px;
                            padding: 8px 12px;
                            background: #FEF3C7;
                            border: 1px solid #F59E0B;
                            border-radius: 4px;
                            font-size: 12px;
                            color: #92400E;
                        }
                    }
                }
            }

            .modal-actions {
                display: flex;
                gap: 12px;
                justify-content: flex-end;

                button {
                    padding: 10px 20px;
                    border-radius: 6px;
                    font-size: 14px;
                    font-weight: 500;
                    cursor: pointer;
                    border: 1px solid #D1D5DB;
                    background: white;
                    color: #374151;
                    transition: all 0.2s;

                    &:hover:not(:disabled) {
                        background: #F9FAFB;
                        border-color: #9CA3AF;
                    }

                    &.primary {
                        background: #10B981;
                        color: white;
                        border-color: #10B981;

                        &:hover:not(:disabled) {
                            background: #059669;
                            border-color: #059669;
                        }
                    }

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }
        }
    }
}
