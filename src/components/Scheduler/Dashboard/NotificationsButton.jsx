import { useState } from 'react'
import { useQuery } from '@apollo/client'

import GetUserNotificationsQuery from '../../../graphql/queries/components/Scheduler/Dashboard/Notifications'
import NotificationsPanel from './NotificationsPanel'

const NotificationsButton = () => {
    const [showPanel, setShowPanel] = useState(false)

    // Query to get unread count
    const { data } = useQuery(GetUserNotificationsQuery, {
        variables: { first: 50 }, // Get enough to count unread
        pollInterval: 30000, // Poll every 30 seconds for new notifications
    })

    const notifications = data?.me?.notifications?.edges?.map(edge => edge.node) || []
    const unreadCount = notifications.filter(notif => !notif.readAtIso).length

    const handleTogglePanel = () => {
        setShowPanel(!showPanel)
    }

    const handleClosePanel = () => {
        setShowPanel(false)
    }

    return (
        <div className="notifications-container">
            <button 
                className="notifications-button"
                onClick={handleTogglePanel}
                title="Notifications"
            >
                <img
                            className='bell-icon'
                            alt='bell-icon'
                            src={require('../../../theme/assets/bell-icon.png')}
                            style={{ width: "35px", height: "35px" }}
                        />
                {unreadCount > 0 && (
                    <span className="notification-badge">
                        {unreadCount > 99 ? '99+' : unreadCount}
                    </span>
                )}
            </button>

            {showPanel && (
                <div className="notifications-overlay">
                    <div className="notifications-backdrop" onClick={handleClosePanel} />
                    <NotificationsPanel onClose={handleClosePanel} />
                </div>
            )}
        </div>
    )
}

export default NotificationsButton
