import { useState } from 'react'
import useConstants from '../../../../hooks/useConstants'

import Checkbox from '../../../Shared/Checkbox'
import IconButton from '../../../Shared/IconButton'
import ModalPane from '../../../Shared/ModalPane'

const SetupInsurancesPane = ({ handleChange, insurances: arr, onHide }) => {
    // Filter out empty strings and convert to integers
    const initialSelected = arr && arr[0] !== "" ? arr.map(o => parseInt(o)).filter(id => !isNaN(id)) : []
    console.log('SetupInsurancesPane - received insurances:', arr)
    console.log('SetupInsurancesPane - initialSelected:', initialSelected)
    const [selected, setSelected] = useState(initialSelected)
    const { insurances } = useConstants()
    console.log('SetupInsurancesPane - available insurances:', insurances)

    function handleInsurance(id, checked) {
        if (checked) {
            // add
            setSelected([...selected, id])
        } else {
            // remove
            const i = selected.findIndex(idx => idx === id)

            setSelected([...selected.slice(0, i), ...selected.slice(i + 1, selected.length)])
        }
    }

    function save(hide) {
        // If no insurances selected, send [""] to signal backend to clear
        const finalSelection = selected.length === 0 ? [""] : selected
        handleChange('insurances', finalSelection)
        hide()
    }

    return (
        <ModalPane
            className='setup-insurances-pane'
            content={hide =>
                <>
                    <div className='modal-header-frame' >
                        <div className='modal-header'>Select Insurances</div>

                        <img
                            alt='x-icon'
                            className='x-icon'
                            src={require('../../../../theme/assets/x-icon.png')}
                            onClick={hide}
                        />
                    </div>

                    <ul className='insurances-list-view scroll'>
                        {insurances.map(insurance => {
                            const { carrier, id, type } = insurance
                            const checked = selected.includes(id)

                            return (
                                <li className='insurance-cell' key={id}>
                                    <Checkbox
                                        onChange={isChecked => handleInsurance(id, isChecked)}
                                        title={`${carrier} ${type ? `(${type})` : ''}`}
                                        value={checked}
                                    />
                                </li>
                            )
                        })}
                    </ul>

                    <div className='modal-bottom-toolbar-frame'>
                        <IconButton
                            icon={require('../../../../theme/assets/check-icon-white.png')}
                            iconHeight={9.17}
                            background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                            onClick={() => save(hide)}
                            title={{
                                color: 'white',
                                label: 'Save'
                            }}
                        />
                    </div>
                </>
            }
            hide={onHide}
        />
    )
}

export default SetupInsurancesPane