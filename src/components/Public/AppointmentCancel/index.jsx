import { useEffect, useState } from "react"
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router"
import moment from "moment"

import FetchAppointmentToCancelQuery from "../../../graphql/queries/components/Scheduler/Appointment/FetchToCancel"
import ApptCancelMutation from "../../../graphql/mutations/Appointment/Cancel"
import createKioskClient from "../../../graphql/KioskApolloClient"
import useKioskAuth from "../../../hooks/useKioskAuth"

import LoadingPane from "../../Shared/LoadingPane"
import IconButton from "../../Shared/IconButton"

const AppointmentCancel = () => {
    const { orgId, appointmentId } = useParams()
    const navigate = useNavigate()
    const { authenticateKiosk } = useKioskAuth()
    
    const [loading, setLoading] = useState(true)
    const [cancelling, setCancelling] = useState(false)
    const [appointmentData, setAppointmentData] = useState(null)
    const [error, setError] = useState(null)
    const [cancelled, setCancelled] = useState(false)

    useEffect(() => {
        fetchAppointmentDetails()
    }, [orgId, appointmentId])

    const fetchAppointmentDetails = async () => {
        try {
            setLoading(true)
            
            // Authenticate with kiosk user
            const token = await authenticateKiosk()
            
            // Create kiosk client with the token
            const kioskClient = createKioskClient(token)
            
            // Fetch appointment details
            const response = await kioskClient.query({
                query: FetchAppointmentToCancelQuery,
                variables: {
                    orgId,
                    appointmentId
                }
            })

            if (response.data?.org?.appointment) {
                setAppointmentData(response.data.org)
            } else {
                setError('Appointment not found')
            }
        } catch (err) {
            console.error('Failed to fetch appointment:', err)
            setError('Failed to load appointment details')
        } finally {
            setLoading(false)
        }
    }

    const handleCancelAppointment = async () => {
        try {
            setCancelling(true)
            
            // Authenticate with kiosk user
            const token = await authenticateKiosk()
            
            // Create kiosk client with the token
            const kioskClient = createKioskClient(token)
            
            // Cancel the appointment
            const response = await kioskClient.mutate({
                mutation: ApptCancelMutation,
                variables: {
                    input: {
                        id: appointmentId
                    }
                }
            })

            const { errors, success } = response.data.apptCancel

            if (success) {
                setCancelled(true)
            } else {
                setError(errors?.[0]?.message || 'Failed to cancel appointment')
            }
        } catch (err) {
            console.error('Failed to cancel appointment:', err)
            setError('Failed to cancel appointment')
        } finally {
            setCancelling(false)
        }
    }

    if (loading) return <LoadingPane />

    if (error) {
        return (
            <div className='appointment-cancel-view full-screen-view'>
                <div className='error-container'>
                    <h2>Error</h2>
                    <p>{error}</p>
                    <button className='home-button' onClick={() => navigate('/')}>Go to Home</button>
                </div>
            </div>
        )
    }

    if (cancelled) {
        return (
            <div className='appointment-cancel-view full-screen-view'>
                <div className='success-container'>
                    <h2>Appointment Cancelled</h2>
                    <p>Your appointment has been successfully cancelled.</p>                    
                </div>
            </div>
        )
    }

    const { appointment, name: orgName, addresses } = appointmentData
    const appointmentDate = moment(appointment.scheduledAtIso).format('MMMM D, YYYY [at] h:mm A [(ET)]')
    const address = addresses?.[0]?.address || 'Address not available'

    return (
        <div className='appointment-cancel-view full-screen-view'>
            <div className='cancel-container'>
                <img
                    className='wellup-logo'
                    alt='wellup-logo'
                    src={require('../../../theme/assets/wellup-logo.png')}
                />
                
                <h2>Cancel Appointment Confirmation</h2>
                
                <p>Are you sure you want to cancel the appointment scheduled for <strong>{appointmentDate}</strong> at:</p>
                
                <div className='location-info'>
                    <strong>{orgName}</strong><br />
                    {address}
                </div>
                
                <div className='appointment-details'>
                    <p><strong>Service:</strong> {appointment.service?.title || 'Not specified'}</p>
                    <p><strong>With:</strong> {appointment.provider?.fullName || 'Not specified'}</p>
                </div>
                
                <div className='action-buttons'>
                    <IconButton
                        background='linear-gradient(180deg, #E42B57 0%, #C41E3A 100%)'
                        onClick={handleCancelAppointment}
                        disabled={cancelling}
                        title={{
                            color: 'white',
                            label: cancelling ? 'Cancelling...' : 'Yes, Cancel Appointment'
                        }}
                    />
                    
                    <IconButton
                        background='linear-gradient(180deg, #008390 0%, #006B75 100%)'
                        onClick={() => window.close()}
                        title={{
                            color: 'white',
                            label: 'Keep Appointment'
                        }}
                    />
                </div>
            </div>
        </div>
    )
}

export default AppointmentCancel
