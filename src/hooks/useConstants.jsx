// IANA Timezone Database constants and other application constants

const useConstants = () => {
    const apptKinds = {
        appointment: {
            title: 'In-Facility',
            color: '#262D2D'
        },
        queue: {
            title: 'Queue',
            color: '#E97100'
        },
        'walk-in': {
            title: 'Walk-In',
            color: '#E97100'
        }
    }

    const apptStatuses = {
        arrived: {
            title: 'Checked In',
            color: '#E97100',
            desc: 'Member was checked in by staff.',
            showDuration: true
        },
        booked: {
            title: 'Booked',
            color: '#747A7A',
            desc: 'Appointment was booked by the member through the wellup application.'
        },
        cancel: {
            title: 'Cancelled',
            color: 'red'
        },
        cancelled: {
            title: 'Cancelled',
            color: 'red',
            desc: 'Member cancelled their appointment.'
        },
        complete: {
            title: 'Complete',
            color: '#0CA44C'
        },
        completed: {
            title: 'Complete',
            color: '#0CA44C',
            desc: 'Appointment was marked complete.',
            showDuration: true
        },
        check_in: {
            title: 'Check In',
            color: '#E97100'
        },
        left: {
            title: 'LWBS',
            color: 'red',
            desc: 'Member left the facility.'
        },
        ignore: {
            title: 'LWBS',
            color: 'red',
            desc: 'Member cheked in and then left the facility.'
        },
        ignored: {
            title: 'LWBS',
            color: 'red',
            desc: 'Member cheked in and then left the facility.'
        },
        no_show: {
            title: 'No Show',
            color: 'red',
            desc: 'Member did not show up or check into the facility.'
        },
        pending: {
            title: 'Pending',
            color: 'grey'
        },
        ready: {
            title: 'Ready',
            color: '#529FF9',
            desc: 'Member information was verified and ready to begin visit.',
            showDuration: true
        },
        roomed: {
            title: 'In Progress',
            color: '#008390',
            showDuration: true
        },
        start: {
            title: 'Start',
            color: '#008390'
        },
        active: {
            title: 'In Session',
            color: '#00904fff',
            desc: 'Member information was verified and ready to begin visit.',
        }
        
    }

    const genders = [
        { id: 'male', value: 'Male' },
        { id: 'female', value: 'Female' },
        { id: 'non_binary', value: 'Non-Binary' },
        { id: 'trans', value: 'Trans' }
    ]

    const languages = [
        { id: 'en', value: 'English' },
        { id: 'es', value: 'Spanish' },
        { id: 'fr', value: 'French' },
        { id: 'de', value: 'German' },
        { id: 'it', value: 'Italian' },
        { id: 'pt', value: 'Portuguese' },
        { id: 'ru', value: 'Russian' },
        { id: 'zh', value: 'Chinese (Mandarin)' },
        { id: 'ja', value: 'Japanese' },
        { id: 'ko', value: 'Korean' },
        { id: 'ar', value: 'Arabic' },
        { id: 'hi', value: 'Hindi' },
        { id: 'th', value: 'Thai' },
        { id: 'vi', value: 'Vietnamese' },
        { id: 'pl', value: 'Polish' },
        { id: 'nl', value: 'Dutch' },
        { id: 'sv', value: 'Swedish' },
        { id: 'da', value: 'Danish' },
        { id: 'no', value: 'Norwegian' },
        { id: 'fi', value: 'Finnish' }
    ]

    const ethnicities = [
        { id: 'asian', value: 'Asian' },
        { id: 'hispanic', value: 'Hispanic/Latino' },
        { id: 'white', value: 'White' },
        { id: 'black', value: 'Black/African American' },
        { id: 'native_american', value: 'Native American' },
        { id: 'pacific_islander', value: 'Pacific Islander' },
        { id: 'middle_eastern', value: 'Middle Eastern' },
        { id: 'mixed', value: 'Mixed/Multiracial' },
        { id: 'other', value: 'Other' },
        { id: 'prefer_not_to_say', value: 'Prefer not to say' }
    ]

    const insurances = [
        { id: 0, carrier: 'Uninsured Members', type: '' },
{ id: 1, carrier: 'Aetna', type: 'PPO' },
{ id: 2, carrier: 'AvMed', type: 'HMO' },
{ id: 3, carrier: 'Blue Cross Blue Shield', type: 'PPO' },
{ id: 4, carrier: 'Cigna', type: 'PPO' },
{ id: 5, carrier: 'Humana', type: 'PPO' },
{ id: 6, carrier: 'Kaiser Permanente', type: 'HMO' },
{ id: 7, carrier: 'Molina Healthcare', type: 'HMO' },
{ id: 8, carrier: 'Oscar Health', type: 'EPO' },
{ id: 9, carrier: 'UnitedHealthcare', type: 'PPO' },
{ id: 10, carrier: 'WellCare', type: 'HMO' }
    ]

    const orgKinds = [
        { id: 'community_partner', value: 'Community Partner' },
        { id: 'facility', value: 'Facility' },
    ]

    const serviceKinds = [
        { id: 'appointment', value: 'Appointment' },
        // { id: 'provider_appt', value: 'Provider Appointment' },
        { id: 'queue', value: 'Queue' }
    ]

    const staffRoles = [
        { id: 'admin', value: 'Admin' },
        { id: 'provider', value: 'Provider' },
        { id: 'staff', value: 'Staff' }
    ]

    const usaStates = [
        { id: 'AL', value: 'Alabama' },
        { id: 'AK', value: 'Alaska' },
        { id: 'AZ', value: 'Arizona' },
        { id: 'AR', value: 'Arkansas' },
        { id: 'CA', value: 'California' },
        { id: 'CO', value: 'Colorado' },
        { id: 'CT', value: 'Connecticut' },
        { id: 'DE', value: 'Delaware' },
        { id: 'FL', value: 'Florida' },
        { id: 'GA', value: 'Georgia' },
        { id: 'HI', value: 'Hawaii' },
        { id: 'ID', value: 'Idaho' },
        { id: 'IL', value: 'Illinois' },
        { id: 'IN', value: 'Indiana' },
        { id: 'IA', value: 'Iowa' },
        { id: 'KS', value: 'Kansas' },
        { id: 'KY', value: 'Kentucky' },
        { id: 'LA', value: 'Louisiana' },
        { id: 'ME', value: 'Maine' },
        { id: 'MD', value: 'Maryland' },
        { id: 'MA', value: 'Massachusetts' },
        { id: 'MI', value: 'Michigan' },
        { id: 'MN', value: 'Minnesota' },
        { id: 'MS', value: 'Mississippi' },
        { id: 'MO', value: 'Missouri' },
        { id: 'MT', value: 'Montana' },
        { id: 'NE', value: 'Nebraska' },
        { id: 'NV', value: 'Nevada' },
        { id: 'NH', value: 'New Hampshire' },
        { id: 'NJ', value: 'New Jersey' },
        { id: 'NM', value: 'New Mexico' },
        { id: 'NY', value: 'New York' },
        { id: 'NC', value: 'North Carolina' },
        { id: 'ND', value: 'North Dakota' },
        { id: 'OH', value: 'Ohio' },
        { id: 'OK', value: 'Oklahoma' },
        { id: 'OR', value: 'Oregon' },
        { id: 'PA', value: 'Pennsylvania' },
        { id: 'RI', value: 'Rhode Island' },
        { id: 'SC', value: 'South Carolina' },
        { id: 'SD', value: 'South Dakota' },
        { id: 'TN', value: 'Tennessee' },
        { id: 'TX', value: 'Texas' },
        { id: 'UT', value: 'Utah' },
        { id: 'VT', value: 'Vermont' },
        { id: 'VA', value: 'Virginia' },
        { id: 'WA', value: 'Washington' },
        { id: 'WV', value: 'West Virginia' },
        { id: 'WI', value: 'Wisconsin' },
        { id: 'WY', value: 'Wyoming' },
    ];

    const timezones = [
        // US Timezones (Most Common)
        { id: 'America/New_York', value: 'Eastern Time (New York)' },
        { id: 'America/Chicago', value: 'Central Time (Chicago)' },
        { id: 'America/Denver', value: 'Mountain Time (Denver)' },
        { id: 'America/Los_Angeles', value: 'Pacific Time (Los Angeles)' },
        { id: 'America/Anchorage', value: 'Alaska Time (Anchorage)' },
        { id: 'Pacific/Honolulu', value: 'Hawaii Time (Honolulu)' },

        // US Regional Variations
        { id: 'America/Phoenix', value: 'Mountain Standard Time (Phoenix)' },
        { id: 'America/Detroit', value: 'Eastern Time (Detroit)' },
        { id: 'America/Indianapolis', value: 'Eastern Time (Indianapolis)' },
        { id: 'America/Louisville', value: 'Eastern Time (Louisville)' },
        { id: 'America/Menominee', value: 'Central Time (Menominee)' },
        { id: 'America/North_Dakota/Center', value: 'Central Time (North Dakota)' },

        // Canada
        { id: 'America/Toronto', value: 'Eastern Time (Toronto)' },
        { id: 'America/Vancouver', value: 'Pacific Time (Vancouver)' },
        { id: 'America/Winnipeg', value: 'Central Time (Winnipeg)' },
        { id: 'America/Halifax', value: 'Atlantic Time (Halifax)' },
        { id: 'America/St_Johns', value: 'Newfoundland Time (St. Johns)' },

        // Mexico
        { id: 'America/Mexico_City', value: 'Central Time (Mexico City)' },
        { id: 'America/Tijuana', value: 'Pacific Time (Tijuana)' },
        { id: 'America/Cancun', value: 'Eastern Time (Cancun)' },

        // Major World Cities
        { id: 'Europe/London', value: 'Greenwich Mean Time (London)' },
        { id: 'Europe/Paris', value: 'Central European Time (Paris)' },
        { id: 'Europe/Berlin', value: 'Central European Time (Berlin)' },
        { id: 'Europe/Rome', value: 'Central European Time (Rome)' },
        { id: 'Europe/Madrid', value: 'Central European Time (Madrid)' },
        { id: 'Europe/Amsterdam', value: 'Central European Time (Amsterdam)' },
        { id: 'Europe/Brussels', value: 'Central European Time (Brussels)' },
        { id: 'Europe/Zurich', value: 'Central European Time (Zurich)' },
        { id: 'Europe/Vienna', value: 'Central European Time (Vienna)' },
        { id: 'Europe/Stockholm', value: 'Central European Time (Stockholm)' },
        { id: 'Europe/Oslo', value: 'Central European Time (Oslo)' },
        { id: 'Europe/Copenhagen', value: 'Central European Time (Copenhagen)' },
        { id: 'Europe/Helsinki', value: 'Eastern European Time (Helsinki)' },
        { id: 'Europe/Warsaw', value: 'Central European Time (Warsaw)' },
        { id: 'Europe/Prague', value: 'Central European Time (Prague)' },
        { id: 'Europe/Budapest', value: 'Central European Time (Budapest)' },
        { id: 'Europe/Bucharest', value: 'Eastern European Time (Bucharest)' },
        { id: 'Europe/Athens', value: 'Eastern European Time (Athens)' },
        { id: 'Europe/Istanbul', value: 'Turkey Time (Istanbul)' },
        { id: 'Europe/Moscow', value: 'Moscow Standard Time (Moscow)' },

        // Asia Pacific
        { id: 'Asia/Tokyo', value: 'Japan Standard Time (Tokyo)' },
        { id: 'Asia/Shanghai', value: 'China Standard Time (Shanghai)' },
        { id: 'Asia/Hong_Kong', value: 'Hong Kong Time (Hong Kong)' },
        { id: 'Asia/Singapore', value: 'Singapore Standard Time (Singapore)' },
        { id: 'Asia/Seoul', value: 'Korea Standard Time (Seoul)' },
        { id: 'Asia/Taipei', value: 'Taipei Standard Time (Taipei)' },
        { id: 'Asia/Bangkok', value: 'Indochina Time (Bangkok)' },
        { id: 'Asia/Jakarta', value: 'Western Indonesian Time (Jakarta)' },
        { id: 'Asia/Manila', value: 'Philippine Standard Time (Manila)' },
        { id: 'Asia/Kuala_Lumpur', value: 'Malaysia Time (Kuala Lumpur)' },
        { id: 'Asia/Kolkata', value: 'India Standard Time (Kolkata)' },
        { id: 'Asia/Dubai', value: 'Gulf Standard Time (Dubai)' },
        { id: 'Asia/Riyadh', value: 'Arabia Standard Time (Riyadh)' },
        { id: 'Asia/Tehran', value: 'Iran Standard Time (Tehran)' },

        // Australia & New Zealand
        { id: 'Australia/Sydney', value: 'Australian Eastern Time (Sydney)' },
        { id: 'Australia/Melbourne', value: 'Australian Eastern Time (Melbourne)' },
        { id: 'Australia/Brisbane', value: 'Australian Eastern Time (Brisbane)' },
        { id: 'Australia/Perth', value: 'Australian Western Time (Perth)' },
        { id: 'Australia/Adelaide', value: 'Australian Central Time (Adelaide)' },
        { id: 'Australia/Darwin', value: 'Australian Central Time (Darwin)' },
        { id: 'Pacific/Auckland', value: 'New Zealand Standard Time (Auckland)' },

        // South America
        { id: 'America/Sao_Paulo', value: 'Brasilia Time (São Paulo)' },
        { id: 'America/Buenos_Aires', value: 'Argentina Time (Buenos Aires)' },
        { id: 'America/Santiago', value: 'Chile Standard Time (Santiago)' },
        { id: 'America/Lima', value: 'Peru Time (Lima)' },
        { id: 'America/Bogota', value: 'Colombia Time (Bogotá)' },
        { id: 'America/Caracas', value: 'Venezuela Time (Caracas)' },

        // Africa
        { id: 'Africa/Cairo', value: 'Eastern European Time (Cairo)' },
        { id: 'Africa/Johannesburg', value: 'South Africa Standard Time (Johannesburg)' },
        { id: 'Africa/Lagos', value: 'West Africa Time (Lagos)' },
        { id: 'Africa/Nairobi', value: 'East Africa Time (Nairobi)' },
        { id: 'Africa/Casablanca', value: 'Western European Time (Casablanca)' },

        // UTC
        { id: 'UTC', value: 'Coordinated Universal Time (UTC)' },
    ];

    return {
        apptKinds, apptStatuses,
        ethnicities,
        genders,
        insurances,
        languages,
        orgKinds,
        serviceKinds,
        staffRoles,
        timezones,
        usaStates
    }
}

export default useConstants