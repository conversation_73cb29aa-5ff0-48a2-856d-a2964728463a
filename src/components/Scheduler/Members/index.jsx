import { useEffect, useState } from "react"
import { useQuery } from "@apollo/client"
import moment from "moment"

import AppointmentCreateUsersQuery from "../../../graphql/queries/components/Scheduler/Appointment/CreateUsers"
import UserFindOrCreateMutation from "../../../graphql/mutations/User/FindOrCreate"
import UserUpdateMutation from "../../../graphql/mutations/User/Update"
import UserAddDataMutation from "../../../graphql/mutations/User/AddData"
import createKioskClient from "../../../graphql/KioskApolloClient"

import useCurrentRole from "../../../hooks/useCurrentRole"
import useKioskAuth from "../../../hooks/useKioskAuth"
import useConstants from "../../../hooks/useConstants"
import { sendSMS } from "../../../services/NotificationService"

import LoadingPane from "../../Shared/LoadingPane"
import TableView from "../../Shared/TableView"
import IconButton from "../../Shared/IconButton"
import Avatar from "../../Shared/Avatar"
import FloatingTextField from "../../Shared/FloatingTextField"
import SelectMenu from "../../Shared/SelectMenu"
import CheckboxGroup from "../../Shared/CheckboxGroup"

const SchedulerMembers = () => {
    const [onload, setOnload] = useState(true)
    const [members, setMembers] = useState([])
    const [showAddForm, setShowAddForm] = useState(false)
    const [refreshTrigger, setRefreshTrigger] = useState(0)
    const [newMember, setNewMember] = useState({
        firstName: '',
        lastName: '',
        dob: '',
        ethnicity: []
    })
    const [requesting, setRequesting] = useState(false)
    const [memberToEdit, setMemberToEdit] = useState(null)
    const [editMemberData, setEditMemberData] = useState({
        firstName: '',
        lastName: '',
        dob: '',
        email: '',
        phone: '',
        gender: '',
        mainLanguage: '',
        disabilities: '',
        ethnicity: [],
        street: '',
        street2: '',
        city: '',
        state: '',
        zip: ''
    })

    // SMS Modal State
    const [smsModalOpen, setSmsModalOpen] = useState(false)
    const [smsRecipient, setSmsRecipient] = useState(null)
    const [smsMessage, setSmsMessage] = useState('')
    const [smsSending, setSmsSending] = useState(false)

    const { currentRole } = useCurrentRole()
    const { authenticateKiosk } = useKioskAuth()
    const constants = useConstants()

    const { data, loading, refetch } = useQuery(AppointmentCreateUsersQuery, {
        variables: { id: currentRole?.org?.id },
        skip: !currentRole?.org?.id
    })

    useEffect(() => {
        if (!loading && data?.org?.users?.nodes) {
            const formattedMembers = data.org.users.nodes.map(user => {
                const email = user?.emails?.find(o => o.label === 'main')
                const phone = user?.phones?.find(o => o.label === 'main')

                return {
                    ...user,
                    firstNameFrame: (
                        <div className='name-frame'>
                            <Avatar
                                width={32}
                                user={user}
                            />
                            <div className='cell-label'>{user.firstName || '-'}</div>
                        </div>
                    ),
                    lastNameFrame: (
                        <div className='cell-label'>{user.lastName || '-'}</div>
                    ),
                    dobFrame: (
                        <div className='cell-label'>
                            {user.dob ? moment(user.dob).format('M/D/YYYY') : '-'}
                        </div>
                    ),
                    phoneFrame: (
                        <div className='phone-cell'>
                            <span className='cell-label'>{phone?.number || '-'}</span>
                            {phone?.number && (
                                <button
                                    className='sms-button'
                                    onClick={(e) => {
                                        e.stopPropagation()
                                        handleOpenSMS(user)
                                    }}
                                    title={`Send SMS to ${user.firstName} ${user.lastName}`}
                                >
                                    SMS
                                </button>
                            )}
                        </div>
                    ),
                    emailFrame: (
                        <div className='cell-label'>{email?.address || '-'}</div>
                    ),
                    actionsFrame: (
                        <div className='actions-frame'>
                            {user?.emails?.find(o => o.label === 'main')?.address !== process.env.REACT_APP_KIOSK_EMAIL ? (
                                <>
                                    <button
                                        className='edit-button'
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            handleEditMember(user)
                                        }}
                                        title="Edit member"
                                    >
                                        Edit
                                    </button>

                                    <button
                                        className='delete-button'
                                        onClick={(e) => {
                                            e.stopPropagation()
                                            handleDeleteMember(user)
                                        }}
                                        title="Remove member from organization"
                                    >
                                        Delete
                                    </button>
                                </>
                            ) : (
                                <div className='kiosk-user-label' title="Kiosk user cannot be edited or deleted">
                                    System User
                                </div>
                            )}
                        </div>
                    )
                }
            })
            setMembers(formattedMembers)
            setOnload(false)
        }
    }, [loading, data, refreshTrigger])

    const refreshMembersList = async () => {
        try {
            await refetch()
            setRefreshTrigger(prev => prev + 1) // Force re-render
        } catch (error) {
            console.error('Error refreshing members list:', error)
        }
    }

    const handleInputChange = (field, value) => {
        setNewMember(prev => ({
            ...prev,
            [field]: value
        }))
    }

    const handleAddMember = async () => {
        if (!newMember.firstName || !newMember.lastName) {
            alert('First Name and Last Name are required')
            return
        }

        try {
            setRequesting(true)

            // Authenticate with kiosk user for admin permissions
            const token = await authenticateKiosk()

            // Create kiosk client with the token for mutations
            const kioskClient = createKioskClient(token)

            const input = {
                firstName: newMember.firstName,
                lastName: newMember.lastName,
                org: currentRole?.org?.id,
                role: "person"
            }

            // Add optional fields if provided
            if (newMember.dob) {
                input.dob = newMember.dob
            }

            const response = await kioskClient.mutate({
                mutation: UserFindOrCreateMutation,
                variables: { input }
            })

            if (response.data?.userFindOrCreate?.success) {
                const createdUser = response.data.userFindOrCreate.user

                // Save ethnicity data if provided using correct GraphQL structure
                if (newMember.ethnicity && newMember.ethnicity.length > 0) {
                    try {
                        const currentTimestamp = Math.floor(Date.now() / 1000) // Convert to seconds for 32-bit Int
                        const endTimestamp = currentTimestamp + (10 * 365 * 24 * 60 * 60) // 10 years from now in seconds

                        const ethnicityResponse = await kioskClient.mutate({
                            mutation: UserAddDataMutation,
                            variables: {
                                input: {
                                    user: createdUser.id, // Target the specific member, not kiosk user
                                    kind: "ethnicity",
                                    name: "selected ethnicity",
                                    beginEpoch: currentTimestamp,
                                    data: [{
                                        key: "person.ethnicity",
                                        data: newMember.ethnicity,
                                        beginEpoch: currentTimestamp,
                                        endEpoch: endTimestamp
                                    }]
                                }
                            }
                        })
                        console.log('Ethnicity data saved for new member:', ethnicityResponse.data)
                    } catch (ethnicityError) {
                        console.error('Error saving ethnicity data:', ethnicityError)
                        // Don't fail the whole operation for ethnicity error
                    }
                }

                // Reset form and refresh data
                setNewMember({
                    firstName: '',
                    lastName: '',
                    dob: '',
                    ethnicity: []
                })
                setShowAddForm(false)
                refreshMembersList()
            } else {
                const errors = response.data?.userFindOrCreate?.errors || []
                alert(`Error adding member: ${errors.map(e => e.message).join(', ')}`)
            }
        } catch (error) {
            console.error('Error adding member:', error)
            alert('Failed to add member. Please try again.')
        } finally {
            setRequesting(false)
        }
    }

    const handleCancelAdd = () => {
        setNewMember({
            firstName: '',
            lastName: '',
            dob: '',
            ethnicity: []
        })
        setShowAddForm(false)
    }

    const handleEditMember = (user) => {
        const email = user?.emails?.find(o => o.label === 'main')
        const phone = user?.phones?.find(o => o.label === 'main')
        const gender = user?.latestData?.find(o => o.key === 'person.gender')
        const ethnicity = user?.latestData?.find(o => o.key === 'person.ethnicity')
        const address = user?.addresses?.find(o => o.label === 'main')

        console.log('Loading member for edit:', {
            userId: user.id,
            latestData: user.latestData,
            ethnicityData: ethnicity,
            ethnicityValues: ethnicity?.values
        })

        // Parse combined street field back into separate fields
        let street = '', street2 = ''
        if (address?.street) {
            const streetParts = address.street.split(', ')
            street = streetParts[0] || ''
            street2 = streetParts.length > 1 ? streetParts.slice(1).join(', ') : ''
        }

        setMemberToEdit(user)
        setEditMemberData({
            firstName: user.firstName || '',
            lastName: user.lastName || '',
            dob: user.dob || '',
            email: email?.address || '',
            phone: phone?.number || '',
            gender: gender?.values?.[0] || '',
            mainLanguage: user.mainLanguage || '',
            disabilities: user.disabilities || '',
            ethnicity: ethnicity?.values || [],
            street: street,
            street2: street2,
            city: address?.city || '',
            state: address?.state || '',
            zip: address?.zip || ''
        })
    }

    const handleUpdateMember = async () => {
        if (!memberToEdit || !editMemberData.firstName || !editMemberData.lastName) {
            alert('First Name and Last Name are required')
            return
        }

        try {
            setRequesting(true)

            // Authenticate with kiosk user for admin permissions
            const token = await authenticateKiosk()

            // Create kiosk client with the token for mutations
            const kioskClient = createKioskClient(token)

            const input = {
                id: memberToEdit.id,
                firstName: editMemberData.firstName,
                lastName: editMemberData.lastName,
                // Preserve existing roles to maintain organization membership
                // Map roles to include orgId and role fields expected by RoleInput
                roles: memberToEdit.roles.map(role => ({
                    orgId: role.org?.id || currentRole?.org?.id,
                    role: role.role
                }))
            }

            // Add optional fields if provided
            if (editMemberData.dob) {
                input.dob = editMemberData.dob
            }

            if (editMemberData.gender) {
                input.gender = editMemberData.gender
            }

            if (editMemberData.mainLanguage) {
                input.mainLanguage = editMemberData.mainLanguage
            }

            if (editMemberData.disabilities) {
                input.disabilities = editMemberData.disabilities
            }

            // Handle emails array
            if (editMemberData.email) {
                input.emails = [{ address: editMemberData.email, label: "main" }]
            }

            // Handle phones array
            if (editMemberData.phone) {
                input.phones = [{ number: editMemberData.phone, label: "main" }]
            }

            // Handle address if any address fields are provided
            const { street, street2, city, state, zip } = editMemberData
            if (street || street2 || city || state || zip) {
                                
                input.addresses = [{
                    label: "main",
                    city: city || "",
                    state: state || "",
                    street: street || "",
                    street2: street2 || "",
                    zip: zip || ""
                }]
            }

            const response = await kioskClient.mutate({
                mutation: UserUpdateMutation,
                variables: {
                    orgId: currentRole?.org?.id,
                    input
                }
            })

            if (response.data?.userUpdate?.success) {
                // Save ethnicity data using correct GraphQL structure (including empty array to clear existing data)
                try {
                    const currentTimestamp = Math.floor(Date.now() / 1000) // Convert to seconds for 32-bit Int
                    const endTimestamp = currentTimestamp + (10 * 365 * 24 * 60 * 60) // 10 years from now in seconds

                    const ethnicityResponse = await kioskClient.mutate({
                        mutation: UserAddDataMutation,
                        variables: {
                            input: {
                                user: memberToEdit.id, // Target the specific member, not kiosk user
                                kind: "ethnicity",
                                name: "selected ethnicity",
                                beginEpoch: currentTimestamp,
                                data: [{
                                    key: "person.ethnicity",
                                    data: editMemberData.ethnicity || [],
                                    beginEpoch: currentTimestamp,
                                    endEpoch: endTimestamp
                                }]
                            }
                        }
                    })
                    console.log('Ethnicity data saved for edited member:', ethnicityResponse.data)
                } catch (ethnicityError) {
                    console.error('Error saving ethnicity data:', ethnicityError)
                    // Don't fail the whole operation for ethnicity error
                }

                // Reset form and refresh data
                setMemberToEdit(null)
                setEditMemberData({
                    firstName: '',
                    lastName: '',
                    dob: '',
                    email: '',
                    phone: '',
                    gender: '',
                    mainLanguage: '',
                    disabilities: '',
                    ethnicity: [],
                    street: '',
                    street2: '',
                    city: '',
                    state: '',
                    zip: ''
                })
                refreshMembersList()
            } else {
                const errors = response.data?.userUpdate?.errors || []
                alert(`Error updating member: ${errors.map(e => e.message).join(', ')}`)
            }
        } catch (error) {
            console.error('Error updating member:', error)
            alert('Failed to update member. Please try again.')
        } finally {
            setRequesting(false)
        }
    }

    const handleCancelEdit = () => {
        setMemberToEdit(null)
        setEditMemberData({
            firstName: '',
            lastName: '',
            dob: '',
            email: '',
            phone: '',
            gender: '',
            mainLanguage: '',
            disabilities: '',
            ethnicity: [],
            street: '',
            street2: '',
            city: '',
            state: '',
            zip: ''
        })
    }

    // SMS Functions
    const handleOpenSMS = (user) => {
        const phone = user?.phones?.find(o => o.label === 'main')
        if (phone?.number) {
            setSmsRecipient({
                id: user.id,
                name: `${user.firstName} ${user.lastName}`,
                phone: phone.number
            })
            setSmsMessage('')
            setSmsModalOpen(true)
        }
    }

    const handleCloseSMS = () => {
        setSmsModalOpen(false)
        setSmsRecipient(null)
        setSmsMessage('')
        setSmsSending(false)
    }

    const calculateSMSSegments = (message) => {
        if (!message) return 0
        const length = message.length
        if (length <= 160) return 1
        return Math.ceil(length / 153) // SMS segments after first are 153 chars
    }

    const handleSendSMS = async () => {
        if (!smsMessage.trim() || !smsRecipient?.phone) return

        try {
            setSmsSending(true)
            await sendSMS(smsRecipient.phone, smsMessage)
            alert(`SMS sent successfully to ${smsRecipient.name}`)
            handleCloseSMS()
        } catch (error) {
            console.error('Error sending SMS:', error)
            alert(`Failed to send SMS: ${error.message}`)
        } finally {
            setSmsSending(false)
        }
    }

    const handleEditInputChange = (field, value) => {
        setEditMemberData(prev => ({
            ...prev,
            [field]: value
        }))
    }

    const handleDeleteMember = async (user) => {
        const kioskEmail = process.env.REACT_APP_KIOSK_EMAIL

        // Prevent deletion of kiosk user
        const userEmail = user?.emails?.find(o => o.label === 'main')?.address
        if (userEmail === kioskEmail) {
            alert('Cannot remove kiosk user from organization')
            return
        }

        if (!window.confirm(`Are you sure you want to remove ${user.firstName} ${user.lastName} from this organization?`)) {
            return
        }

        try {
            setRequesting(true)

            // Authenticate with kiosk user for admin permissions
            const token = await authenticateKiosk()
            const kioskClient = createKioskClient(token)

            // Filter out the role for current organization, keep all others
            const updatedRoles = user.roles
                .filter(role => role.org?.id !== currentRole?.org?.id)
                .map(role => ({
                    orgId: role.org?.id,
                    role: role.role
                }))

            const input = {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                roles: updatedRoles // Remove current org role, keep others
            }

            // Add other existing fields to preserve them
            if (user.dob) input.dob = user.dob
            if (user.emails?.length) {
                input.emails = user.emails.map(email => ({
                    address: email.address,
                    label: email.label
                }))
            }
            if (user.phones?.length) {
                input.phones = user.phones.map(phone => ({
                    number: phone.number,
                    label: phone.label
                }))
            }

            const response = await kioskClient.mutate({
                mutation: UserUpdateMutation,
                variables: {
                    orgId: currentRole?.org?.id,
                    input
                }
            })

            if (response.data?.userUpdate?.success) {
                refreshMembersList() // Refresh the member list
            } else {
                const errors = response.data?.userUpdate?.errors || []
                alert(`Error removing member: ${errors.map(e => e.message).join(', ')}`)
            }
        } catch (error) {
            console.error('Error removing member:', error)
            alert('Failed to remove member. Please try again.')
        } finally {
            setRequesting(false)
        }
    }

    if (onload || loading || !currentRole?.org?.id) return <LoadingPane />

    return (
        <div className='members-view full-screen-view'>
            {requesting && <LoadingPane style={{ position: 'fixed' }} />}
            
            <div className='toolbar'>
                <div className='toolbar-header'>{`Members (${members.length})`}</div>
                
                <div className='right-section'>
                    <IconButton
                        icon={require('../../../theme/assets/plus-icon-white.png')}
                        iconHeight={12}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={() => setShowAddForm(true)}
                        title={{
                            color: 'white',
                            label: 'Add Member'
                        }}
                    />
                </div>
            </div>

            {showAddForm && (
                <div className='add-member-form'>
                    <div className='form-header'>Add New Member</div>
                    <div className='form-fields'>
                        <input
                            type="text"
                            placeholder="First Name *"
                            value={newMember.firstName}
                            onChange={(e) => handleInputChange('firstName', e.target.value)}
                        />
                        <input
                            type="text"
                            placeholder="Last Name *"
                            value={newMember.lastName}
                            onChange={(e) => handleInputChange('lastName', e.target.value)}
                        />
                        <input
                            type="date"
                            placeholder="Date of Birth"
                            value={newMember.dob}
                            onChange={(e) => handleInputChange('dob', e.target.value)}
                        />
                        <div className='ethnicity-field'>
                            <CheckboxGroup
                                label='Ethnicity (Optional)'
                                options={constants.ethnicities}
                                value={newMember.ethnicity}
                                onChange={(value) => handleInputChange('ethnicity', value)}
                                placeholder='Select one or more ethnicities'
                            />
                        </div>
                    </div>
                    <div className='form-actions'>
                        <button onClick={handleCancelAdd}>Cancel</button>
                        <button onClick={handleAddMember} className='primary'>Save</button>
                    </div>
                </div>
            )}

            {memberToEdit && (
                <div className='edit-member-modal'>
                    <div className='modal-background' onClick={handleCancelEdit} />
                    <div className='modal-content'>
                        <div className='modal-header'>Edit Member</div>
                        <div className='modal-body'>
                            <div className='edit-form-sections'>
                                <div className='form-section'>
                                    <div className='section-header'>Basic Information</div>
                                    <div className='form-fields'>
                                        <FloatingTextField
                                            label='First Name'
                                            onChange={text => handleEditInputChange('firstName', text)}
                                            value={editMemberData.firstName}
                                        />

                                        <FloatingTextField
                                            label='Last Name'
                                            onChange={text => handleEditInputChange('lastName', text)}
                                            value={editMemberData.lastName}
                                        />

                                        <input
                                            type="date"
                                            placeholder="Date of Birth"
                                            value={editMemberData.dob}
                                            onChange={(e) => handleEditInputChange('dob', e.target.value)}
                                            style={{
                                                padding: '12px',
                                                border: '1px solid #ddd',
                                                borderRadius: '6px',
                                                marginBottom: '12px'
                                            }}
                                        />

                                        <FloatingTextField
                                            label='Email'
                                            onChange={text => handleEditInputChange('email', text)}
                                            value={editMemberData.email}
                                            type='email'
                                        />

                                        <FloatingTextField
                                            label='Phone'
                                            onChange={text => handleEditInputChange('phone', text)}
                                            value={editMemberData.phone}
                                            type='tel'
                                        />

                                        <SelectMenu
                                            label='Gender (Optional)'
                                            options={constants.genders}
                                            onChange={value => handleEditInputChange('gender', value)}
                                            placeholder='Select Gender'
                                            value={editMemberData.gender}
                                        />

                                        <SelectMenu
                                            label='Main Language (Optional)'
                                            options={constants.languages}
                                            onChange={value => handleEditInputChange('mainLanguage', value)}
                                            placeholder='Select primary language'
                                            value={editMemberData.mainLanguage}
                                        />

                                        <FloatingTextField
                                            label='Disabilities/Accessibility Needs (Optional)'
                                            onChange={text => handleEditInputChange('disabilities', text)}
                                            placeholder='Describe any accessibility accommodations needed'
                                            value={editMemberData.disabilities}
                                            multiline={true}
                                        />

                                        <div className='ethnicity-field'>
                                            <CheckboxGroup
                                                label='Ethnicity (Optional)'
                                                options={constants.ethnicities}
                                                value={editMemberData.ethnicity}
                                                onChange={(value) => handleEditInputChange('ethnicity', value)}
                                                placeholder='Select one or more ethnicities'
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className='form-section'>
                                    <div className='section-header'>Address Information (Optional)</div>
                                    <div className='form-fields'>
                                        <FloatingTextField
                                            label='Street Address'
                                            onChange={text => handleEditInputChange('street', text)}
                                            placeholder='Enter street address'
                                            value={editMemberData.street}
                                        />

                                        <FloatingTextField
                                            label='Street Address 2'
                                            onChange={text => handleEditInputChange('street2', text)}
                                            placeholder='Apartment, suite, etc. (optional)'
                                            value={editMemberData.street2}
                                        />

                                        <FloatingTextField
                                            label='City'
                                            onChange={text => handleEditInputChange('city', text)}
                                            placeholder='Enter city'
                                            value={editMemberData.city}
                                        />

                                        <FloatingTextField
                                            label='State'
                                            onChange={text => handleEditInputChange('state', text)}
                                            placeholder='Enter state'
                                            value={editMemberData.state}
                                        />

                                        <FloatingTextField
                                            label='ZIP Code'
                                            onChange={text => handleEditInputChange('zip', text)}
                                            placeholder='Enter ZIP code'
                                            value={editMemberData.zip}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div className='modal-actions'>
                            <button onClick={handleCancelEdit}>Cancel</button>
                            <button onClick={handleUpdateMember} className='primary'>Update Member</button>
                        </div>
                    </div>
                </div>
            )}

            {smsModalOpen && smsRecipient && (
                <div className='sms-modal'>
                    <div className='modal-background' onClick={handleCloseSMS} />
                    <div className='modal-content'>
                        <div className='modal-header'>
                            Send SMS to {smsRecipient.name}
                        </div>
                        <div className='modal-body'>
                            <div className='sms-form'>
                                <div className='recipient-info'>
                                    <span className='recipient-label'>To:</span>
                                    <span className='recipient-phone'>{smsRecipient.phone}</span>
                                </div>

                                <div className='message-section'>
                                    <label className='message-label'>Message:</label>
                                    <textarea
                                        className='message-textarea'
                                        value={smsMessage}
                                        onChange={(e) => setSmsMessage(e.target.value)}
                                        placeholder='Type your message here...'
                                        maxLength={500}
                                        rows={6}
                                    />

                                    <div className='character-counter'>
                                        <span className='char-count'>
                                            {smsMessage.length} / 500 characters
                                        </span>
                                        <span className='segment-count'>
                                            — {calculateSMSSegments(smsMessage)} segment{calculateSMSSegments(smsMessage) !== 1 ? 's' : ''}
                                        </span>
                                    </div>

                                    {smsMessage.length >= 400 && (
                                        <div className='warning-message'>
                                            ⚠️ Long messages may incur additional costs
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className='modal-actions'>
                            <button onClick={handleCloseSMS} disabled={smsSending}>
                                Cancel
                            </button>
                            <button
                                onClick={handleSendSMS}
                                className='primary'
                                disabled={!smsMessage.trim() || smsSending}
                            >
                                {smsSending ? 'Sending...' : 'Send SMS'}
                            </button>
                        </div>
                    </div>
                </div>
            )}

            <div className='content-frame'>
                {members?.length ? (
                    <div className='fullscreen-table'>
                        <TableView
                            data={members}
                            headers={[
                                { title: 'First Name', width: '20%' },
                                { title: 'Last Name', width: '20%' },
                                { title: 'Date of Birth', width: '20%' },
                                { title: 'Phone', width: '20%' },
                                { title: 'Email', width: '20%' },
                                { title: 'Actions', width: '10%' }
                            ]}
                            keys={['firstNameFrame', 'lastNameFrame', 'dobFrame', 'phoneFrame', 'emailFrame', 'actionsFrame']}
                        />
                    </div>
                ) : (
                    <div className='hint-frame'>
                        <div className='hint-label'>No members have been added to this organization.</div>
                        {/* <button className='hint-button' onClick={() => setShowAddForm(true)}>+ Add Member</button> */}
                    </div>
                )}
            </div>
        </div>
    )
}

export default SchedulerMembers
