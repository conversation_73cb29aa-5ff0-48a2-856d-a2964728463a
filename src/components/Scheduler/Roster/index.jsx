import { useEffect, useState } from 'react'

import _ from 'lodash'
import { useQuery } from '@apollo/client'
import useCurrentRole from '../../../hooks/useCurrentRole'
import useKioskAuth from '../../../hooks/useKioskAuth'

import SchedulerRosterQuery from '../../../graphql/queries/components/Scheduler/Roster'
import UserUpdateMutation from '../../../graphql/mutations/User/Update'
import createKioskClient from '../../../graphql/KioskApolloClient'

import Avatar from '../../Shared/Avatar'
import LoadingPane from '../../Shared/LoadingPane'
import IconButton from '../../Shared/IconButton'
import TableView from '../../Shared/TableView'

const SetupRoster = (props) => {
    const { navigate } = props
    const [onload, setOnload] = useState(true)
    const [users, setUsers] = useState([])
    const [refreshTrigger, setRefreshTrigger] = useState(0)
    const [requesting, setRequesting] = useState(false)
    const { currentRole } = useCurrentRole()
    const { authenticateKiosk } = useKioskAuth()

    const { data, loading, refetch } = useQuery(SchedulerRosterQuery, {
        variables: { id: currentRole?.org?.id },
        skip: !currentRole?.org?.id
    })

    useEffect(() => {
        if (!loading && data?.org?.users?.nodes) {
            setUsers(data.org.users.nodes.filter(u => !u.roles.find(r => r.role === 'person')).map(u => {
                const email = u.emails.find(o => o.label === 'main')
                const phone = u.phones.find(o => o.label === 'main')
                const role = u.roles.find(o => o.org?.id === currentRole?.org?.id)

                return {
                    ...u,
                    fullName: `${u.firstName} ${u.lastName}`,
                    email: email?.address ?? '-',
                    phone: phone?.number ?? '-',
                    role: _.startCase(role.role)
                }
            }))
            setOnload(false)
        }
    }, [loading, data, refreshTrigger])

    const refreshStaffList = async () => {
        try {
            await refetch()
            setRefreshTrigger(prev => prev + 1) // Force re-render
        } catch (error) {
            console.error('Error refreshing staff list:', error)
        }
    }

    function goToStaffRoute(id) {
        navigate(`/dashboard/setup/roster/${id}`)
    }

    const handleDeleteStaff = async (user) => {
        const kioskEmail = process.env.REACT_APP_KIOSK_EMAIL

        // Prevent deletion of kiosk user
        const userEmail = user?.emails?.find(o => o.label === 'main')?.address
        if (userEmail === kioskEmail) {
            alert('Cannot remove kiosk user from organization')
            return
        }

        if (!window.confirm(`Are you sure you want to remove ${user.firstName} ${user.lastName} from this organization?`)) {
            return
        }

        try {
            setRequesting(true)

            // Authenticate with kiosk user for admin permissions
            const token = await authenticateKiosk()
            const kioskClient = createKioskClient(token)

            // Filter out the role for current organization, keep all others
            const updatedRoles = user.roles
                .filter(role => role.org?.id !== currentRole?.org?.id)
                .map(role => ({
                    orgId: role.org?.id,
                    role: role.role
                }))

            const input = {
                id: user.id,
                firstName: user.firstName,
                lastName: user.lastName,
                roles: updatedRoles // Remove current org role, keep others
            }

            // Add other existing fields to preserve them
            if (user.dob) input.dob = user.dob
            if (user.emails?.length) {
                input.emails = user.emails.map(email => ({
                    address: email.address,
                    label: email.label
                }))
            }
            if (user.phones?.length) {
                input.phones = user.phones.map(phone => ({
                    number: phone.number,
                    label: phone.label
                }))
            }

            const response = await kioskClient.mutate({
                mutation: UserUpdateMutation,
                variables: {
                    orgId: currentRole?.org?.id,
                    input
                }
            })

            if (response.data?.userUpdate?.success) {
                refreshStaffList() // Refresh the staff list
            } else {
                const errors = response.data?.userUpdate?.errors || []
                alert(`Error removing staff member: ${errors.map(e => e.message).join(', ')}`)
            }
        } catch (error) {
            console.error('Error removing staff member:', error)
            alert('Failed to remove staff member. Please try again.')
        } finally {
            setRequesting(false)
        }
    }

    if (onload || !currentRole?.org?.id) return <LoadingPane />

    return (
        <div className='setup-roster-view'>
            <div className='toolbar' style={{ padding: 0 }}>
                <div className='toolbar-header'>{`Staff (${users.length})`}</div>

                <div className='right-section'>
                    <IconButton
                        icon={require('../../../theme/assets/plus-icon-white.png')}
                        iconHeight={12}
                        background='linear-gradient(180deg, #FD8205 0%, #E97100 100%)'
                        onClick={() => goToStaffRoute('add')}
                        title={{
                            color: 'white',
                            label: 'Add Staff'
                        }}
                    />
                </div>
            </div>

            {users?.length ?
                <TableView
                    headers={[
                        { title: 'Name', width: '25%' },
                        { title: 'Role', width: '20%' },
                        { title: 'Email', width: '25%' },
                        { title: 'Phone', width: '20%' },
                        { title: 'Actions', width: '10%' },
                    ]}
                    data={users.map(user => {
                        return {
                            ...user,
                            nameFrame: {
                                sort: 'fullName',
                                component: <div className='name-frame'>
                                    <Avatar
                                        width={32}
                                        user={user}
                                    />

                                    <div className='cell-label full-name ellipsis'>{user.fullName}</div>
                                </div>
                            },
                            actionsFrame: (
                                <div className='actions-frame'>
                                    {user?.emails?.find(o => o.label === 'main')?.address !== process.env.REACT_APP_KIOSK_EMAIL ? (
                                        <>
                                            <button
                                                className='edit-button'
                                                onClick={(e) => {
                                                    e.stopPropagation()
                                                    goToStaffRoute(user.id)
                                                }}
                                                title="Edit staff member"
                                            >x
                                                Edit
                                            </button>

                                            <button
                                                className='delete-button'
                                                onClick={(e) => {
                                                    e.stopPropagation()
                                                    handleDeleteStaff(user)
                                                }}
                                                title="Remove staff member from organization"
                                            >
                                                Delete
                                            </button>
                                        </>
                                    ) : (
                                        <div className='kiosk-user-label' title="Kiosk user cannot be edited or deleted">
                                            System User
                                        </div>
                                    )}
                                </div>
                            )
                        }
                    })}
                    keys={['nameFrame', 'role', 'email', 'phone', 'actionsFrame']}
                    onCellClick={(user) => goToStaffRoute(user.id)}
                />
                :
                <div className='hint-frame'>
                    No staff members have been added.
                    <button className='hint-button' onClick={() => goToStaffRoute('add')}>+ Add Staff Member</button>
                </div>
            }
        </div>
    )
}

export default SetupRoster